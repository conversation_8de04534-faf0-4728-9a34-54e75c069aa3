<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .modal-body,
    .modal-header,
    .modal-footer {
        background-color: white;
    }

    .modal {
        background-color: white;
        margin: 70px;
    }
</style>

<div class="row" ng-init="init()">
    <div class="col-lg-12">
        <br>
        <h1 class="page-header">
            Loyalty Reclaim
        </h1>
    </div>
    <div class="row">
        <div class="col-lg-1">
            <div class="row" style="display:flex;">
                <button class="btn btn-primary" style="margin:8px" title="Reclaim loyalty points for specific customer"
                    ng-click="toggleReclaimForm('POINTS')">
                    Reclaim Points
                </button>
                <button class="btn btn-primary" style="margin:8px" title="Reclaim all loyalty points across the system"
                    ng-click="toggleReclaimForm('ALL_POINTS')">
                    Reclaim All Points
                </button>
            </div>
        </div>
    </div>

    <!-- Single Reclaim Form -->
    <div class="row" ng-if="showReclaimForm" style="margin-top: 20px;">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>{{reclaimType === 'POINTS' ? 'Reclaim Points' : 'Reclaim All Points'}}</h4>
                </div>
                <div class="panel-body">
                    <form novalidate role="form" name="reclaimForm" autocomplete="off">
                        <div class="row" style="margin: 15px;">
                            <div class="col-xs-3">
                                <label for="customerPhone">Customer Phone Number</label>
                            </div>
                            <div class="col-xs-9">
                                <input type="text" name="customerPhone" id="customerPhone"
                                       ng-model="customerPhone" autocomplete="off"
                                       placeholder="Enter 10-digit phone number"
                                       class="form-control"
                                       ng-class="{'is-valid': customerPhone && customerPhone.length === 10, 'is-invalid': customerPhone && customerPhone.length > 0 && customerPhone.length !== 10}"
                                       maxlength="10" pattern="[0-9]{10}" required
                                       ng-change="onPhoneNumberChange()" />
                                <small class="text-muted" ng-if="!customerPhone || customerPhone.length === 0">Enter exactly 10 digits</small>
                                <small class="text-success" ng-if="customerPhone && customerPhone.length === 10">✓ Valid phone number</small>
                                <small class="text-danger" ng-if="customerPhone && customerPhone.length > 0 && customerPhone.length !== 10">Phone number must be exactly 10 digits</small>
                            </div>
                        </div>
                        <div class="row" style="margin: 15px;">
                            <div class="col-xs-3">
                                <label for="startDate">Start Date</label>
                            </div>
                            <div class="col-xs-9">
                                <input class="form-control" data-ng-model="startDate" type="date" 
                                       id="startDate" name="startDate"
                                       placeholder="yyyy-mm-dd" required 
                                       min="{{getStartDateMin()}}" 
                                       max="{{getStartDateMax()}}" 
                                       ng-change="onStartDateChange()" />
                                <small class="text-muted" ng-if="reclaimType === 'POINTS'">
                                    Must be within the past year ({{oneYearAgoDate}} to {{todayDate}})
                                </small>
                            </div>
                        </div>
                        <div class="row" style="margin: 15px;">
                            <div class="col-xs-3">
                                <label for="endDate">End Date</label>
                            </div>
                            <div class="col-xs-9">
                                <input class="form-control" data-ng-model="endDate" type="date" 
                                       id="endDate" name="endDate"
                                       placeholder="yyyy-mm-dd" required 
                                       min="{{getEndDateMin()}}" 
                                       max="{{getEndDateMax()}}" />
                                <small class="text-muted" ng-if="reclaimType === 'POINTS'">
                                    Must be on or after start date and not in the future
                                </small>
                            </div>
                        </div>
                        <div class="row" style="margin: 15px;">
                            <div class="col-xs-12">
                                <button class="btn btn-danger" ng-click="closeReclaimForm()">Cancel</button>
                                <button class="btn btn-primary" ng-click="processReclaim()"
                                        ng-disabled="dataLoading" style="margin-left: 10px;">
                                    <span ng-if="dataLoading">Loading...</span>
                                    <span ng-if="!dataLoading">Find</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div class="row" ng-if="showResults && loyaltyReclaimData" style="margin-top: 20px;">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>Loyalty Reclaim Details</h4>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>Customer Name</th>
                                    <th>Total Acquired Points</th>
                                    <th>Total Expired Points</th>
                                    <th>Total Redeemed Points</th>
                                    <th>Points to Reclaim</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="data in loyaltyReclaimData">
                                    <td>{{data.customerName || 'N/A'}}</td>
                                    <td>{{data.totalAcquiredPoints || 0}}</td>
                                    <td>{{data.totalExpiredPoints || 0}}</td>
                                    <td>{{data.totalRedeemedPoints || 0}}</td>
                                    <td><strong>{{data.pointsToReclaim || 0}}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-xs-12">
                            <button class="btn btn-success" ng-click="performReclaim()"
                                    ng-disabled="dataLoading">
                                <span ng-if="dataLoading">Processing...</span>
                                <span ng-if="!dataLoading">Reclaim Points</span>
                            </button>
                            <button class="btn btn-default" ng-click="closeReclaimForm()" style="margin-left: 10px;">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
