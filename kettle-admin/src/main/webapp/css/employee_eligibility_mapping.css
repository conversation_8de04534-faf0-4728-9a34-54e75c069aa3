* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            padding: 0 30px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 20px;
            position: relative;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: #3498db;
            color: white;
            transform: scale(1.1);
        }

        .step.completed .step-number {
            background: #27ae60;
            color: white;
        }

        .step-label {
            margin-left: 10px;
            font-weight: 600;
            color: #666;
        }

        .step.active .step-label {
            color: #3498db;
        }

        .step-connector {
            width: 60px;
            height: 2px;
            background: #e0e0e0;
            margin: 0 10px;
        }

        .main-content {
            padding: 30px;
        }

        .search-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #3498db;
            height: 150px;
            justify-content: space-between;
            align-items: center;
        }

        .search-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #2c3e50;
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-control {

            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 24px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            align-self: flex-end;
            width: 120px;
            margin:auto;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .employee-list {
            background: white;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            overflow: hidden;
        }

        .list-header {
            background: #f1f2f6;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .list-title {
            font-weight: 600;
            color: #2c3e50;
        }

        .select-all {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-weight: 500;
            color: #3498db;
        }

        .employee-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
        }

        .employee-item:hover {
            background: #f8f9fa;
        }

        .employee-checkbox {
            width: 18px;
            height: 18px;
            accent-color: #3498db;
        }

        .employee-info {
            flex: 1;
            flex-direction: column;
        }

        .employee-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .employee-details {
            color: #666;
            font-size: 14px;
        }

        .employee-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .selected-summary {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .selected-count {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .next-btn {
            background: white;
            color: black;
            border: 2px solid white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mapping-section {
            display: none;
        }

        .mapping-section.active {
            display: block;
        }

        .selected-employees {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #2196f3;
        }

        .employees-chips {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .employee-chip {
            background: #2196f3;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .chip-remove {
            background: rgba(255,255,255,0.3);
            border: none;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
        }

        .eligibility-tabs {
            display: flex;
            background: #f1f2f6;
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 30px;
        }

        .tab-btn {
            flex: 1;
            padding: 15px;
            border: none;
            background: transparent;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }

        .tab-btn.active {
            background: white;
            color: #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .mapping-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .mapping-tab {
            flex: 1;
            padding: 12px;
            border: none;
            background: transparent;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }

        .mapping-tab.active {
            background: #3498db;
            color: white;
        }

        .mapping-form {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 25px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .add-mapping-btn {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
        }

        .add-mapping-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }

        .current-mappings {
            margin-top: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }

        .mappings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .mappings-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .mappings-count {
            background: #3498db;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .mappings-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .mapping-search {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
            width: 200px;
            transition: all 0.3s ease;
        }

        .mapping-search:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .mapping-filter {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .mappings-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .mapping-item {
            background: white;
            border-radius: 12px;
            padding: 18px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .mapping-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .mapping-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-align: center;
            min-width: 60px;
        }

        .unit-badge {
            background: #e3f2fd;
            color: #1976d2;
        }

        .city-badge {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .region-badge {
            background: #e8f5e8;
            color: #388e3c;
        }

        .mapping-info {
            flex: 1;
        }

        .mapping-value {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 6px;
        }

        .mapping-employees {
            font-size: 14px;
            color: #666;
        }

        .employee-count {
            font-weight: 500;
            color: #3498db;
        }

        .employee-names {
            color: #666;
        }

        .mapping-actions-btn {
            display: flex;
            gap: 8px;
        }

        .edit-mapping, .remove-mapping {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .edit-mapping {
            color: #3498db;
        }

        .edit-mapping:hover {
            background: #e3f2fd;
        }

        .remove-mapping {
            color: #e74c3c;
        }

        .remove-mapping:hover {
            background: #ffebee;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-subtext {
            font-size: 14px;
            opacity: 0.7;
        }

        .action-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
            border-top: 2px solid #e0e0e0;
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bulk-actions {
            display: flex;
            gap: 15px;
        }

        .bulk-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .bulk-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }

        .save-btn {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .hidden {
            display: none !important;
        }

        .individual-selection-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .individual-employees {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
            margin-top: 15px;
        }

        .individual-employee-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .individual-employee-item:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .individual-employee-item.selected {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .individual-checkbox {
            width: 16px;
            height: 16px;
            accent-color: #3498db;
        }

        .individual-emp-info {
            flex: 1;
        }

        .individual-emp-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .individual-emp-id {
            color: #666;
            font-size: 12px;
            margin-top: 2px;
        }

        .selection-summary {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
            color: #856404;
            font-size: 14px;
            display: none;
        }

        .selection-summary.show {
            display: block;
        }

        @media (max-width: 768px) {
            .filters-row {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .step-indicator {
                flex-direction: column;
                align-items: center;
            }

            .step {
                margin: 10px 0;
            }

            .step-connector {
                display: none;
            }

            .action-buttons {
                flex-direction: column;
                gap: 15px;
            }

            .bulk-actions {
                flex-direction: column;
                width: 100%;
            }
        }

        /* Additional styles for Angular integration */
        .employee-chip {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 0.85rem;
            border: 1px solid #bbdefb;
        }

        .empty-chips {
            color: white;
            font-style: italic;
            padding: 10px;
        }

        .individual-employee-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }

        .individual-employee-item:last-child {
            border-bottom: none;
        }

        .region-badge {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .empty-text {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

.empty-subtext {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Searchable dropdown styles */
.searchable-dropdown {
    position: relative;
}

.searchable-dropdown select {
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 30px;
}

.unit-filters-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #17a2b8;
}

.unit-filters-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.unit-selection-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.form-control.searchable {
    background-color: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    height: 100px;
    overflow-y: auto;
}

.form-control.searchable:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.unit-info-display {
    background: #e8f5e8;
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    padding: 12px;
    margin-top: 10px;
    font-size: 14px;
    color: #2e7d32;
}

.filter-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
    font-size: 13px;
    color: #856404;
}

.clear-filters-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.clear-filters-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.get-units-section {
    margin-top: 15px;
    text-align: right;
}

.get-units-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.get-units-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
}

/* Review Section Styles */
.review-section {
    padding: 30px;
    background: white;
    border-radius: 15px;
    margin: 20px 0;
}

.review-header {
    text-align: center;
    margin-bottom: 30px;
}

.review-subtitle {
    color: #666;
    font-size: 16px;
    margin-top: 10px;
}

.review-summary {
    margin-bottom: 30px;
}

.summary-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 25px;
    border-left: 5px solid #3498db;
}

.summary-header h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: black;
    text-transform: uppercase;
    font-weight: 500;
}

.review-mappings {
    margin-bottom: 30px;
}

.mapping-review-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mapping-review-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.mapping-review-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.mapping-type-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    text-align: center;
    min-width: 80px;
}

.mapping-details {
    flex: 1;
}

.mapping-target {
    font-size: 18px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.mapping-employees-info {
    font-size: 14px;
    color: #666;
}

.employee-list {
    color: #555;
    margin-left: 8px;
}

.review-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 2px solid #e0e0e0;
}

.confirm-btn {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.confirm-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Searchable dropdown container */
.searchable-dropdown-container {
    position: relative;
    width: 100%;
}

.searchable-input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    background-color: #fff;
    cursor: pointer;
}

.searchable-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.dropdown-options.show {
    display: block;
}

.dropdown-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.dropdown-option:hover {
    background-color: #f8f9fa;
}

.dropdown-option.selected {
    background-color: #3498db;
    color: white;
}

.dropdown-option:last-child {
    border-bottom: none;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
}

.pagination-info {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 35px;
}

.pagination-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.pagination-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

.pagination-btn.active {
    background: #27ae60;
    font-weight: bold;
}

.pagination-ellipsis {
    color: #666;
    padding: 8px 4px;
    font-weight: bold;
}

.employee-count-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 10px 0;
    font-size: 14px;
    color: #1976d2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.employee-count-info .count-badge {
    background: #1976d2;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.mapping-tab.disabled {
    opacity: 0.4;
    pointer-events: none;
    cursor: not-allowed;
}

/* Employee Actions and View Icon */
.employee-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.employee-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.view-mapping-icon {
    cursor: pointer;
    color: #3498db;
    font-size: 16px;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    background: rgba(52, 152, 219, 0.1);
}

.view-mapping-icon:hover {
    color: #2980b9;
    background: rgba(52, 152, 219, 0.2);
    transform: scale(1.1);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #ecf0f1;
    text-align: right;
    background: #f8f9fa;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 40px 20px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mapping Items in Modal */
.mappings-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mapping-item-modal {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 15px;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.mapping-item-modal:hover {
    border-color: #3498db;
    background: #ebf3fd;
}

.mapping-type-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    min-width: 60px;
    text-align: center;
}

.mapping-type-badge.unit-badge {
    background: #e8f5e8;
    color: #27ae60;
    border: 1px solid #27ae60;
}

.mapping-type-badge.city-badge {
    background: #fff3cd;
    color: #f39c12;
    border: 1px solid #f39c12;
}

.mapping-type-badge.region-badge {
    background: #f8d7da;
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

.mapping-details-modal {
    flex: 1;
}

.mapping-eligibility {
    font-size: 12px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.mapping-value-modal {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 10px;
}

.mapping-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 12px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.created-info {
    color: #6c757d;
    font-style: italic;
}

/* Empty State */
.empty-mappings {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.empty-text {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.empty-subtext {
    font-size: 14px;
    color: #95a5a6;
}

/* Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Compact Modal Styles */
.mappings-container-compact {
    max-height: 300px;
    overflow-y: auto;
}

.compact-mapping-row {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    margin-bottom: 6px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #3498db;
    font-size: 14px;
}

.compact-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    min-width: 50px;
    text-align: center;
}

.compact-eligibility {
    font-weight: 600;
    color: #2c3e50;
    min-width: 80px;
}

.compact-value {
    flex: 1;
    color: #34495e;
    font-weight: 500;
}

.compact-status {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

.compact-status.active {
    background: #d4edda;
    color: #155724;
}

.compact-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.compact-creator {
    font-size: 12px;
    color: #6c757d;
    min-width: 80px;
}

/* Compact Mapping List Styles */
.mappings-list-compact {
    max-height: 400px;
    overflow-y: auto;
}

.compact-mapping-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    transition: all 0.2s ease;
}

.compact-mapping-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.compact-mapping-content {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.compact-employee-info {
    font-weight: 600;
    color: #2c3e50;
    min-width: 120px;
}

.mapping-arrow {
    font-size: 16px;
    color: #3498db;
    font-weight: bold;
}

.compact-mapping-target {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.compact-type-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    min-width: 45px;
    text-align: center;
}

.compact-mapping-value {
    font-weight: 500;
    color: #34495e;
}

.compact-mapping-actions {
    display: flex;
    gap: 5px;
}

.compact-edit-btn, .compact-remove-btn {
    background: none;
    border: none;
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.compact-edit-btn:hover {
    background: #e3f2fd;
}

.compact-remove-btn:hover {
    background: #ffebee;
}

/* Badge Colors */
.unit-badge {
    background: #e3f2fd;
    color: #1976d2;
}

.city-badge {
    background: #f3e5f5;
    color: #7b1fa2;
}

.region-badge {
    background: #e8f5e8;
    color: #388e3c;
}

/* Task 1: City and Region info display styles */
.city-info-display, .region-info-display {
    margin-top: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

/* Task 2: Employee mapping search styles */
.mapping-search-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.mapping-search-input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.mapping-search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Task 4: Bulk upload modal styles */
.bulk-upload-modal {
    max-width: 1200px;
    width: 95vw;
    max-height: 90vh;
    overflow-y: auto;
}

.upload-summary {
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border-left: 4px solid #3498db;
}

.summary-stats {
    display: flex;
    justify-content: space-around;
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1rem;
    color: black;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.upload-data-preview {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
}

.preview-header h4 {
    margin: 0;
    font-size: 1.2rem;
}

.preview-filters select {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    background: white;
    color: #333;
    font-size: 14px;
}

.preview-table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.preview-table th {
    background: #f8f9fa;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.preview-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.preview-table tr:hover {
    background: #f8f9fa;
}

.valid-row {
    background: #f8fff8;
}

.invalid-row {
    background: #fff5f5;
}

.validation-success {
    color: #28a745;
    font-weight: 600;
    font-size: 12px;
}

.validation-error {
    color: #dc3545;
    font-weight: 600;
    font-size: 12px;
    max-width: 200px;
    word-wrap: break-word;
}

.empty-upload {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-upload .empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-upload .empty-text {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.empty-upload .empty-subtext {
    font-size: 1rem;
    opacity: 0.7;
}

/* Modal footer button styles */
.modal-footer .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.modal-footer .btn-secondary {
    background: #6c757d;
    color: white;
}

.modal-footer .btn-secondary:hover {
    background: #5a6268;
}

.modal-footer .btn-primary {
    background: #3498db;
    color: white;
}

.modal-footer .btn-primary:hover {
    background: #2980b9;
}

.modal-footer .btn-primary:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}


/* Multi-select dropdown styles */
.multi-select-dropdown {
    max-height: 200px;
    overflow-y: auto;
}

.multi-select-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.multi-select-option:hover {
    background-color: #f0f8ff;
}

.multi-select-checkbox {
    margin: 0;
    cursor: pointer;
}

.multi-select-label {
    flex: 1;
    cursor: pointer;
    font-size: 14px;
}

/* Selected items display */
.selected-items-display {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.selected-items-header {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

.selected-items-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.selected-item-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #bbdefb;
}

.remove-selected-item {
    background: none;
    border: none;
    color: #1976d2;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    padding: 0;
    margin-left: 2px;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-selected-item:hover {
    background: #1976d2;
    color: white;
}

/* Tabular mapping display styles */
.mappings-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mappings-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.mappings-table th {
    background: #f8f9fa;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.mappings-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.mapping-table-row:hover {
    background: #f8f9fa;
}

.employee-name-cell {
    font-weight: 600;
    color: #2c3e50;
    min-width: 150px;
}

.mapping-type-cell {
    min-width: 100px;
}

.mapping-names-cell {
    max-width: 300px;
    word-wrap: break-word;
    color: #34495e;
}

.mapping-actions-cell {
    width: 80px;
    text-align: center;
}

.remove-mapping-btn {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    font-size: 16px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.remove-mapping-btn:hover {
    background: #ffebee;
    transform: scale(1.1);
}

/* Duplicate warnings styles */
.duplicate-warnings {
    margin-top: 15px;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    border-left: 4px solid #f39c12;
}

.warning-header {
    font-weight: 600;
    color: black;
    margin-bottom: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.warning-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.warning-item {
    padding: 8px 12px;
    background: #fef9e7;
    border-radius: 4px;
    border-left: 3px solid #f39c12;
}

.warning-text {
    color: #856404;
    font-size: 14px;
    font-weight: 500;
}

/* Enhanced mapping type badges */
.mapping-type-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    min-width: 50px;
    text-align: center;
    display: inline-block;
}

.mapping-type-badge.unit-badge {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.mapping-type-badge.city-badge {
    background: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
}

.mapping-type-badge.region-badge {
    background: #e8f5e8;
    color: #388e3c;
    border: 1px solid #c8e6c9;
}

/* Review table styles */
.review-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.review-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.review-table th {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #5a67d8;
    position: sticky;
    top: 0;
    z-index: 10;
}

.review-table td {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.review-table-row:hover {
    background: #f8f9fa;
}

.review-table-row:nth-child(even) {
    background: #fafbfc;
}

.review-table-row:nth-child(even):hover {
    background: #f1f3f4;
}

.review-employee-cell {
    font-weight: 600;
    color: #2c3e50;
    min-width: 180px;
}

.review-type-cell {
    min-width: 120px;
}

.review-names-cell {
    max-width: 300px;
    word-wrap: break-word;
    color: #34495e;
    font-weight: 500;
}

.review-status-cell {
    min-width: 120px;
}

.status-ready {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: #d4edda;
    color: #155724;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid #c3e6cb;
}

/* Review warnings styles */
.review-warnings {
    margin-top: 15px;
    padding: 15px;
    background: #fff8e1;
    border: 1px solid #ffcc02;
    border-radius: 6px;
    border-left: 4px solid #ff9800;
}

.review-warning-header {
    font-weight: 600;
    color: black;
    margin-bottom: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.review-warning-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.review-warning-item {
    padding: 8px 12px;
    background: #fff3e0;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
}

.review-warning-text {
    color: #e65100;
    font-size: 14px;
    font-weight: 500;
}

/* Responsive design for bulk upload modal */
@media (max-width: 768px) {
    .bulk-upload-modal {
        width: 98vw;
        margin: 1vh auto;
    }

    .summary-stats {
        flex-direction: column;
        gap: 10px;
    }

    .preview-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .preview-table {
        font-size: 12px;
    }

    .preview-table th,
    .preview-table td {
        padding: 8px 4px;
    }

    .mapping-count {
        font-size: 11px;
    }

    .employee-actions {
        min-width: 100px;
    }

    .mappings-table {
        font-size: 12px;
    }

    .mappings-table th,
    .mappings-table td {
        padding: 8px 10px;
    }

    .selected-items-list {
        flex-direction: column;
        gap: 4px;
    }

    .multi-select-dropdown {
        max-height: 150px;
    }

    .review-table {
        font-size: 12px;
    }

    .review-table th,
    .review-table td {
        padding: 10px 8px;
    }

    .review-employee-cell {
        min-width: 120px;
    }

    .review-names-cell {
        max-width: 200px;
    }
}

/* Status Toggle Styles */
.status-toggle-container {
    display: flex;
    align-items: center;
    margin: 0 8px;
}

.status-toggle-btn {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 90px;
    text-align: center;
}

.status-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.status-toggle-btn.active {
    background: #03823c;
    border-color: #27ae60;
    color: white;
}

.status-toggle-btn.inactive {
    background: #ff2151;
    border-color: #e74c3c;
    color: white;
}

.status-toggle-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.status-toggle-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Modal Footer Styles */
.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.modal-footer-left {
    flex: 1;
}

.modal-footer-right {
    display: flex;
    gap: 12px;
    align-items: center;
}

.unsaved-changes-indicator {
    color: #f39c12;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Compact mapping row adjustments for toggle buttons */
.compact-mapping-row {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 8px;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.compact-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
}

.compact-eligibility {
    padding: 4px 8px;
    background: #e3f2fd;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    color: #1976d2;
    min-width: 80px;
    text-align: center;
}

.compact-value {
    flex: 1;
    font-weight: 500;
    color: #2c3e50;
}

.compact-creator {
    font-size: 11px;
    color: #7f8c8d;
    font-style: italic;
    min-width: 100px;
    text-align: right;
}

/* Button loading state */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary:disabled {
    background: #6c757d;
    border-color: #6c757d;
}

/* Responsive adjustments for toggle buttons */
@media (max-width: 768px) {
    .status-toggle-btn {
        min-width: 70px;
        padding: 4px 8px;
        font-size: 10px;
    }

    .compact-mapping-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .status-toggle-container {
        align-self: flex-end;
        margin: 0;
    }

    .modal-footer {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .modal-footer-right {
        justify-content: center;
    }
}

/* Confirmation View Styles */
.confirmation-view {
    padding: 20px;
}

.confirmation-header {
    text-align: center;
    margin-bottom: 25px;
}

.confirmation-header h4 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.confirmation-header p {
    color: #7f8c8d;
    font-size: 1rem;
}

.confirmation-summary {
    margin-bottom: 25px;
}

.confirmation-summary .summary-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.confirmation-summary .stat-item {
    text-align: center;
    padding: 15px 25px;
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.confirmation-summary .stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.confirmation-summary .stat-label {
    font-size: 1.2rem;
    opacity: 0.9;
}

.confirmation-table-container {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.confirmation-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.confirmation-table thead {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
}

.confirmation-table th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.confirmation-table tbody tr {
    border-bottom: 1px solid #ecf0f1;
    transition: background-color 0.2s ease;
}

.confirmation-table tbody tr:hover {
    background-color: #f8f9fa;
}

.confirmation-table tbody tr:last-child {
    border-bottom: none;
}

.confirmation-table td {
    padding: 15px 12px;
    vertical-align: middle;
}

.confirmation-row .mapping-type-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.confirmation-row .eligibility-cell {
    font-weight: 500;
    color: #2c3e50;
}

.confirmation-row .value-cell {
    color: #34495e;
    font-weight: 500;
}

.confirmation-row .status-cell {
    text-align: center;
}

.confirmation-row .status-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.confirmation-row .active-badge {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.confirmation-row .inactive-badge {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.confirmation-indicator {
    color: #3498db;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Responsive Design for Confirmation View */
@media (max-width: 768px) {
    .confirmation-table-container {
        padding: 15px;
        margin: 0 -10px;
    }

    .confirmation-table {
        font-size: 0.85rem;
    }

    .confirmation-table th,
    .confirmation-table td {
        padding: 10px 8px;
    }

    .confirmation-summary .summary-stats {
        flex-direction: column;
        align-items: center;
    }

    .confirmation-summary .stat-item {
        width: 200px;
    }
}
