/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


var adminapp = angular.module('adminapp', ['ngFileUpload', 'ui.bootstrap', 'dndLists', 'ui.router', 'ngCookies',
    '720kb.datepicker', 'ui.grid', 'ui.grid.edit', 'ui.grid.pagination', 'ui.grid.resizeColumns',
    'ui.grid.moveColumns', 'ui.grid.selection', 'ui.grid.exporter', 'ngResource', 'isteven-multi-select',
    'ui.select2', 'disableAll','pathgather.popeye','angularjs-dropdown-multiselect',
     'ui.grid.expandable', 'ui.grid.pinning']);

adminapp.config(['$stateProvider', '$urlRouterProvider', function ($stateProvider, $urlRouterProvider) {

    $stateProvider.state('login', {
        url: '/login',
        templateUrl: 'views/login.html',
        controller: 'LoginController'
    }).state('dashboard', {
        url: '/dashboard',
        templateUrl: 'views/dashboard.html',
        controller: 'MainController'
    }).state('dashboard.unit', {
        url: '/unit',
        templateUrl: 'views/unit.html',
        controller: 'UnitController'
    }).state('dashboard.unit.category', {
        url: '/:category',
        templateUrl: 'views/unitData.html',
        controller: 'UnitDataController'
    }).state('dashboard.kiosOffice', {
        url: '/kiosOffice',
        templateUrl: 'views/kiosOffice.html',
        controller: 'kiosOfficectrl'
    }).state('dashboard.kiosCompany', {
        url: '/kiosCompany',
        templateUrl: 'views/kiosCompany.html',
        controller: 'kiosCompanyctrl'
    }).state('dashboard.kiosLocation', {
        url: '/kiosLocation',
        templateUrl: 'views/kiosLocation.html',
        controller: 'kiosLocationctrl'
    }).state('dashboard.deliveryMapping', {
        url: '/deliveryMapping',
        templateUrl: 'views/deliveryMapping.html',
        controller: 'deliveryMappingctrl'
    }).state('dashboard.unitDeliveryMapping', {
        url: '/unitDeliveryMapping',
        templateUrl: 'views/unitDeliveryMapping.html',
        controller: 'unitDeliveryMappingctrl'
    }).state('dashboard.unitContactsMapping', {
        url: '/unitContactsMapping',
        templateUrl: 'views/unitContactsMapping.html',
        controller: 'unitContactsMappingctrl'
    }).state('dashboard.unitPartnerBrandMetadata', {
        url: '/unitPartnerBrandMetadata',
        templateUrl: 'views/unitPartnerBrandMetadata.html',
        controller: 'unitPartnerBrandMetadataCtrl'
    }).state('dashboard.viewRegionMappings', {
        url: '/viewRegionMappings',
        templateUrl: 'views/viewRegionMappings.html',
        controller: 'viewRegionMappingsCtrl'
    }).state('dashboard.employee', {
        url: '/employee',
        templateUrl: 'views/employee.html',
        controller: 'EmployeeController'
    }).state('dashboard.employeeEligibilityMapping',{
        url: '/employeeEligibilityMapping',
        templateUrl: 'views/employeeEligibilityMapping.html',
        controller: 'employeeEligibilityMappingCtrl'
    }).state('dashboard.reports', {
        url: '/reports',
        templateUrl: 'views/reports.html',
        controller: 'ReportsController'
    }).state('dashboard.brandReports', {
        url: '/brandReports',
        templateUrl: 'views/brandReports.html',
        controller: 'brandReportsCtrl'
    }).state('dashboard.reportSummary', {
        url: '/reportSummary',
        templateUrl: 'views/reportsummary.html',
        controller: 'ReportSummaryController'
    }).state('dashboard.stockReport', {
        url: '/stockReport',
        templateUrl: 'views/stockReport.html',
        controller: 'stockReportController'
    }).state('dashboard.categoryList', {
        url: '/categoryList',
        templateUrl: 'views/categoryList.html',
        controller: 'CategoryListController'
    }).state('dashboard.priceList', {
        url: '/priceList',
        templateUrl: 'views/priceList.html',
        controller: 'PriceListController'
    }).state('dashboard.offerData', {
        url: '/offerData',
        templateUrl: 'views/offerData.html',
        controller: 'OfferDataController'
    }).state('dashboard.offerMappingData', {
        url: '/offerMappingData',
        templateUrl: 'views/offerMappingData.html',
        controller: 'OfferMappingDataController'
    }).state('dashboard.partnerList', {
        url: '/partnerList',
        templateUrl: 'views/partnerList.html',
        controller: 'PartnerDataController'
    }).state('dashboard.couponsData', {
        url: '/couponsData',
        templateUrl: 'views/couponsData.html',
        controller: 'CouponsDataController'
    }).state('dashboard.creditAccounts', {
        url: '/creditAccounts',
        templateUrl: 'views/creditAccounts.html',
        controller: 'CreditAccountsController'
    }).state('dashboard.productData', {
        url: '/productData',
        templateUrl: 'views/productData.html',
        controller: 'ProductDataController'
    }).state('dashboard.priceProfileProductMapping', {
              url: '/priceProfileProductMapping',
              templateUrl: 'views/priceProfileProductMapping.html',
              controller: 'PriceProfileProductMappingCtrl'
          }).state('dashboard.priceProfileCreation', {
            url: '/priceProfileCreation',
            templateUrl: 'views/PriceProfileCreation.html',
            controller: 'ProfileController'
        }).state('dashboard.unitPriceProfile', {
            url: '/unitPriceProfile',
            templateUrl: 'views/UnitPriceProfileMapping.html',
            controller: 'unitPriceProfileController'
    }).state('dashboard.categoryData', {
        url: '/categoryData',
        templateUrl: 'views/categoryData.html',
        controller: 'CategoryDataController'
    }).state('dashboard.referLink', {
        url: '/refer',
        templateUrl: 'views/refer.html',
        controller: 'ReferController'
    }).state('dashboard.cashSettlementsView', {
        url: '/cashSettlementsView',
        templateUrl: 'views/cashSettlementsView.html',
        controller: 'CashSettlementsViewController'
    }).state('dashboard.checkList', {
        url: '/checkList',
        templateUrl: 'views/checkList.html',
        controller: 'checkListDataController'
    }).state('dashboard.cashSettlementsClose', {
        url: '/cashSettlementsClose',
        templateUrl: 'views/cashSettlementsClose.html',
        controller: 'CashSettlementsCloseController'
    }).state('dashboard.addNewRecipe', {
        url: '/addNewRecipe',
        templateUrl: 'views/recipeBuilder.html',
        controller: 'recipeBuilder'
    }).state('dashboard.addMonkRecipe', {
        url: '/addMonkRecipe',
        templateUrl: 'views/monkRecipeBuilder.html',
        controller: 'monkRecipeBuilder'
    }).state('dashboard.manageMonkRecipe', {
        url: '/manageMonkRecipe',
        templateUrl: 'views/manageMonkRecipe.html',
        controller: 'monkRecipeBuilder'
    }).state('dashboard.addNewSCMRecipe', {
        url: '/addNewSCMRecipe',
        templateUrl: 'views/scmRecipeBuilder.html',
        controller: 'recipeBuilder'
    }).state('dashboard.brm', {
        url: '/brm',
        templateUrl: 'views/brmView.html',
        controller: 'brmController'
    }).state('dashboard.brmUnitWise',{
       url: '/brmUnitWise',
       templateUrl: 'views/brmUnitWiseView.html',
       controller : 'brmUnitWiseController'
    }).state('dashboard.findRecipe', {
        url: '/findRecipe',
        templateUrl: 'views/findRecipe.html',
        controller: 'findRecipeController',
        params: {
            onlySCM: false
        }
    }).state('dashboard.findSCMRecipe', {
        url: '/findSCMRecipe',
        templateUrl: 'views/findRecipe.html',
        controller: 'findRecipeController',
        params: {
            onlySCM: true
        }
    }).state('dashboard.accessList', {
        url: '/accessList',
        templateUrl: 'views/accessList.html',
        controller: 'accessListController'
    }).state('dashboard.preAuthenticatedApiMgt', {
        url: '/preAuthenticatedApiMgt',
        templateUrl: 'views/preAuthenticatedApiMgt.html',
        controller: 'preAuthenticatedApiMgtCtrl'
    }).state('dashboard.customerDetail', {
        url: '/customerDetail',
        templateUrl: 'views/customerDetail.html',
        controller: 'customerDetailctrl'
    }).state('dashboard.deliveryMappings', {
        url: '/deliveryMappings',
        templateUrl: 'views/deliveryMappings.html',
        controller: 'deliveryMappingsCtrl'
    }).state('dashboard.partnerEdcMapping', {
    url: '/partnerEdcMapping',
    templateUrl: 'views/partnerEdcMapping.html',
    controller: 'partnerEdcMappingCtrl'
    }).state('dashboard.productWebTags', {
        url: '/productWebTags',
        templateUrl: 'views/productWebTags.html',
        controller: 'productWebTagsctrl'
    }).state('dashboard.employePermissionMapping', {
        url: '/employePermissionMapping',
        templateUrl: 'views/employePermissionMapping.html',
        controller: 'employeePermissionMappingctrl'
    }).state('dashboard.userPolicyManagement', {
        url: '/userPolicyManagement',
        templateUrl: 'views/userPolicyManagement.html',
        controller: 'userPolicyManagementCtrl'
    }).state('dashboard.employeeRolePolicyReset', {
        url: '/employeeRolePolicyReset',
        templateUrl: 'views/employeeRolePolicyReset.html',
        controller: 'employeeRolePolicyResetCtrl'
    }).state('dashboard.taxCategory', {
        url: '/taxCategory',
        templateUrl: 'views/taxCategory.html',
        controller: 'taxCategoryCtrl'
    }).state('dashboard.taxMappings', {
        url: '/taxMappings',
        templateUrl: 'views/taxMappings.html',
        controller: 'taxMappingsCtrl'
    }).state('dashboard.auditReport', {
        url: '/auditReport',
        templateUrl: 'views/auditReport.html',
        controller: 'auditReportCtrl'
    }).state('dashboard.manualBillReport', {
        url: '/manualBillReport',
        templateUrl: 'views/manualBillReport.html',
        controller: 'manualBillReportCtrl'
    }).state('dashboard.activateCashCards', {
        url: '/activateCashCards',
        templateUrl: 'views/activateCashCards.html',
        controller: 'activateCashCardsCtrl'
    }).state('dashboard.SCMRecipeCalculator',{
    	url:'/SCMRecipeCalculator',
    	templateUrl:'views/SCMRecipeCalculator.html',
    	controller:'SCMRecipeCalculatorCtrl'
    }).state('dashboard.SCMRecipeCalculator.SCMRecipeDetails',{
    	url:'/SCMRecipeDetails',
    	templateUrl:'views/SCMRecipeDetails.html',
    	controller:'SCMRecipeDetailsCtrl'
    }).state('dashboard.kettleCache', {
        url: '/kettleCache',
        templateUrl: 'views/kettleCacheManager.html',
        controller: 'kettleCacheCtrl'
    }).state('dashboard.monkConf', {
        url: '/monkConf',
        templateUrl: 'views/monkConf.html',
        controller: 'monkConfCtrl'
    }).state('dashboard.budgetUploader', {
        url: '/budgetUploader',
        templateUrl: 'views/budgetUploader.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.budgetUpdate', {
        url: '/budgetUpdate',
        templateUrl: 'views/budgetUpdate.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.budgetUpdateManpower', {
        url: '/budgetUpdateManpower',
        templateUrl: 'views/budgetUpdateManpower.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.PnLReport', {
        url: '/PnLReport',
        templateUrl: 'views/PnLReport.html',
        controller: 'PnLReportController'
    }).state('dashboard.PnLReportDownload', {
        url: '/PnLReportDownload',
        templateUrl: 'views/PnLReportDownload.html',
        controller: 'PnLReportController'
    }).state('dashboard.budgetUpdateChannelPartner', {
        url: '/budgetUpdateChannelPartner',
        templateUrl: 'views/budgetUpdateChannelPartner.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.budgetUpdateFacilityCharges', {
        url: '/budgetUpdateFacilityCharges',
        templateUrl: 'views/budgetUpdateFacilityCharges.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.budgetUpdateBankCharges', {
        url: '/budgetUpdateBankCharges',
        templateUrl: 'views/budgetUpdateBankCharges.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.budgetUpdateServiceCharges', {
        url: '/budgetUpdateServiceCharges',
        templateUrl: 'views/budgetUpdateServiceCharges.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.penetrationTargets', {
        url: '/penetrationTargets',
        templateUrl: 'views/penetrationTargets.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.regeneratePnL', {
        url: '/regeneratePnL',
        templateUrl: 'views/regeneratePnL.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.regeneratePnLAll', {
        url: '/regeneratePnLAll',
        templateUrl: 'views/regeneratePnLAll.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.regenerateFinalizedPnL', {
        url: '/regenerateFinalizedPnL',
        templateUrl: 'views/regenerateFinalizedPnL.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.regenerateClosedPnL', {
        url: '/regenerateClosedPnL',
        templateUrl: 'views/regenerateClosedPnL.html',
        controller: 'budgetUploadCtrl'
    }).state('dashboard.dsrUploader', {
        url: '/dsrUploader',
        templateUrl: 'views/dsrUploader.html',
        controller: 'dsrUploadCtrl'
    }).state('dashboard.dsrConfiguration', {
        url: '/dsrConfiguration',
        templateUrl: 'views/dsrConfiguration.html',
        controller: 'dsrConfigurationCtrl'
    }).state('dashboard.productDetailsProfile', {
        url: '/productDetailsProfile',
        templateUrl: 'views/productDetails.html',
        controller: 'productDetailsCtrl',
        params: {isProductRecipeMapping: true}
    }).state('dashboard.productDetails', {
        url: '/productDetails',
        templateUrl: 'views/productDetails.html',
        controller: 'productDetailsCtrl',
        params: {isProductRecipeMapping: false}
    }).state('dashboard.productRecipeMapping', {
       url: '/productRecipeMapping',
       templateUrl: 'views/productRecipeMapping.html',
       controller: 'productRecipeMappingCtrl',
       params: {isProductRecipeMapping: true}
    }).state('dashboard.productDetail', {
        url: '/productDetail',
        templateUrl: 'views/productRecipeMapping.html',
        controller: 'productRecipeMappingCtrl',
        params: {isProductRecipeMapping: false}
    }).state('dashboard.manageApps', {
        url: '/manageApps',
        templateUrl: 'views/manageApps.html',
        controller: 'manageAppsCtrl'
    }).state('dashboard.arduinoBuilds', {
        url: '/arduinoBuilds',
        templateUrl: 'views/arduinoBuilds.html',
        controller: 'arduinoBuildsCtrl'
    }).state('dashboard.partnerManagement', {
        url: '/partnerManagement',
        templateUrl: 'views/partnerManagement.html',
        controller: 'partnerManagementCtrl'
    }).state('dashboard.partnerUnitMapping', {
        url: '/partnerUnitMapping',
        templateUrl: 'views/partnerUnitMapping.html',
        controller: 'partnerUnitMappingCtrl'
    }).state('dashboard.partnerMenuManagement', {
        url: '/partnerMenuManagement',
        templateUrl: 'views/partnerMenuManagement.html',
        controller: 'partnerMenuManagementCtrl'
    }).state('dashboard.partnerCategoryManagement', {
        url: '/partnerCategoryManagement',
        templateUrl: 'views/partnerCategoryManagement.html',
        controller: 'partnerCategoryManagementCtrl'
    }).state('dashboard.partnerProductStock', {
        url: '/partnerProductStock',
        templateUrl: 'views/partnerProductStock.html',
        controller: 'partnerProductStockCtrl'
    }).state('dashboard.partnerCafeStatusHistory', {
        url: '/partnerCafeStatusHistory',
        templateUrl: 'views/partnerCafeStatusHistory.html',
        controller: 'partnerCafeStatusHistoryCtrl'
    }).state('dashboard.developerDashboard', {
        url: '/developerDashboard',
        templateUrl: 'views/developerDashboard.html',
        controller: 'developerDashboardCtrl'
    }).state('dashboard.webappLocalities', {
        url: '/webappLocalities',
        templateUrl: 'views/webappLocalities.html',
        controller: 'webappLocalitiesCtrl'
    }).state('dashboard.referral', {
        url: '/referral',
        templateUrl: 'views/referral.html',
        controller: 'referralCtrl'
    }).state('dashboard.appOfferData', {
        url: '/appOfferData',
        templateUrl: 'views/appOfferData.html',
        controller: 'AppOfferDataController'
    }).state('dashboard.appNotification', {
        url: '/appNotification',
        templateUrl: 'views/appNotification.html',
        controller: 'AppNotificationController'
    }).state('dashboard.appSectionData', {
        url: '/appSectionData',
        templateUrl: 'views/appSectionData.html',
        controller: 'AppSectionDataController'
    }).state('dashboard.banner', {
        url: '/banner',
        templateUrl: 'views/banner.html',
        controller: 'BannerController'
    }).state('dashboard.kettleLocalities', {
        url: '/kettleLocalities',
        templateUrl: 'views/kettleLocalities.html',
        controller: 'kettleLocalitiesCtrl'
    }).state('dashboard.UPTCalculations', {
        url: '/UPTCalculations',
        templateUrl: 'views/UPTCalculations.html',
        controller: 'UPTCalculationsCtrl'
    }).state('dashboard.exceptionDay', {
        url: '/exceptionDay',
        templateUrl: 'views/exceptionDay.html',
        controller: 'exceptionDayCtrl'
    }).state('dashboard.uploadMonkRecipe', {
        url: '/uploadMonkRecipe',
        templateUrl: 'views/uploadMonkRecipe.html',
        controller: 'uploadMonkRecipeCtrl'
    }).state('dashboard.CRMAppBanner', {
        url: '/CRMAppBanner',
        templateUrl: 'views/CRMAppBanner.html',
        controller: 'CRMAppBannerCtrl'
    }).state('dashboard.signupCouponDetails', {
        url: '/signupCouponDetails',
        templateUrl: 'views/signupCouponDetails.html',
        controller: 'signupCouponDetailsCtrl'
    }).state('dashboard.historyData',{
        url:'/historyData',
        templateUrl:'views/historyData.html',
        controller: 'historyDataCtrl'
    }).state('dashboard.menuAuditHistory', {
        url: '/menuAuditHistory',
        templateUrl: 'views/menuAuditHistory.html',
        controller: 'menuAuditHistoryCtrl'
    }).state('dashboard.desiChaiCustomProfiles', {
        url: '/desiChaiCustomProfiles',
        templateUrl: 'views/desiChaiCustomProfiles.html',
        controller: 'desiChaiCustomProfilesCtrl'
    }).state('dashboard.priceProfile', {
        url: '/priceProfile',
        templateUrl: 'views/priceProfile.html',
        controller: 'priceProfileCtrl'
    }).state('dashboard.monkMetaData',{
        url:'/monkMetaData',
        templateUrl:'views/monkMetaData.html',
        controller:'monkMetaDataCtrl'
    }).state('dashboard.customerFaceRecognition',{
        url:'/customerFaceRecognition',
        templateUrl:'views/customerFaceRecognition.html',
        controller:'customerFaceRecognitionCtrl'
    }).state('dashboard.addNewRecipeMedia', {
        url: '/addNewRecipeMedia',
        templateUrl: 'views/recipeMediaBuilder.html',
        controller: 'recipeMediaBuilder'
    }).state('dashboard.uploadRecipeCondiment', {
        url: '/uploadRecipeCondiment',
        templateUrl: 'views/recipeCondimentUploader.html',
        controller: 'recipeCondimentUploaderCtrl'
    }).state('dashboard.campaignBuilder', {
        url: '/campaignBuilder',
        templateUrl: 'views/campaignBuilder.html',
        params: {campaignDetail: null},
        controller: 'CampaignBuilderCtrl'
    }).state('dashboard.campaignDetailsView', {
        url: '/campaignDetailsView',
        templateUrl: 'views/campaignDetailsView.html',
        controller: 'CampaignDetailsViewCtrl'
    }).state('dashboard.addLocation', {
        url: '/addLocation',
        templateUrl: 'views/addLocation.html',
        controller: 'addLocationCtrl'
    }).state('dashboard.deliveryCouponUpload', {
        url: '/deliveryCouponUpload',
        templateUrl: 'views/deliveryCouponUpload.html',
        controller: 'DeliveryCouponUploadCtrl'
    }).state('dashboard.paymentModeMapping', {
        url: '/paymentModeMapping',
        templateUrl: 'views/paymentModeMapping.html',
        controller: 'PaymentModeMappingCtrl'
    }).state('dashboard.updatePaymentModeMapping', {
        url: '/updatePaymentModeMapping',
        templateUrl: 'views/updatePaymentModeMapping.html',
        controller: 'UpdatePaymentModeMappingCtrl'
    }).state('dashboard.productPriceBulkUpdate', {
        url: '/productPriceBulkUpdate',
        templateUrl: 'views/productPriceBulkUpdate.html',
        controller: 'productPriceBulkUpdateCtrl'
    }).state('dashboard.employeeBenefit', {
        url: '/employeeBenefit',
        templateUrl: 'views/employeeBenefits.html',
        controller: 'employeeBenefitsCtrl'
    }).state('dashboard.orderManagement', {
        url: '/orderManagement',
        templateUrl: 'views/orderManagement.html',
        controller: 'OrderManagementController'
    }).state('dashboard.uploadCustomer', {
         url: '/uploadCustomer',
         templateUrl: 'views/uploadCustomer.html',
         controller: 'UploadCustomerController'
    }).state('dashboard.droolForOffer',{
        url: '/droolForOffer',
        templateUrl: 'views/droolForOffer.html',
        controller: 'DroolForOfferCtrl'
    }).state('dashboard.manageB2BMonkCustomers', {
        url: '/manageB2BMonkCustomers',
        templateUrl: 'views/manageB2BMonkCustomers.html',
        controller: 'ManageB2BMonkCustomersCtrl'
    }).state('dashboard.viewMonkOrdersAndActions', {
        url: '/viewMonkOrdersAndActions',
        templateUrl: 'views/viewMonkOrdersAndActions.html',
        controller: 'ViewMonkOrdersAndActionsCtrl'
    }).state('dashboard.dataEncryptionDashboard', {
        url: '/dataEncryptionDashboard',
        templateUrl: 'views/dataEncryptionDashboard.html',
        controller: 'DataEncryptionDashboardCtrl'
    }).state('dashboard.dineInAppRestartDashboard', {
        url: '/dineInAppRestartDashboard',
        templateUrl: 'views/dineInAppRestartDashboard.html',
        controller: 'DineInAppRestartDashboardCtrl'
    }).state('dashboard.cashbackOffer', {
              url: '/cashbackOffer',
              templateUrl: 'views/cashbackOffer.html',
              controller: 'CashbackOfferCtrl'
    }).state('dashboard.redeemedCouponUpload', {
         url: '/redeemedCouponUpload',
         templateUrl: 'views/redeemedCouponUploader.html',
         controller: 'redeemedCouponUploaderCtrl'
    }).state('dashboard.versionManagement', {
         url: '/versionDetails',
         templateUrl: 'views/versionDetail.html',
         controller: 'versionDetailCtrl'
    }).state('dashboard.unitVersionManagementNew', {
         url: '/versionManagementNew',
         templateUrl: 'views/versionManagementNew.html',
         controller: 'versionManagementCtrl'
    }).state('dashboard.unitVersionDetail', {
        url: '/unitVersionDetail',
        templateUrl: 'views/unitVersionDetail.html',
        controller: 'unitVersionDetailCtrl'
    }).state('dashboard.unitReportMapping', {
        url: '/unitReportMapping',
        templateUrl: 'views/unitReportMapping.html',
        controller: 'unitReportMappingCtrl'
    }).state('dashboard.unitClosureDetail', {
        url: '/unitClosureDetail',
        templateUrl: 'views/unitClosureDetail.html',
        controller: 'unitClosureDetailCtrl'
    }).state('dashboard.monkRealTimeDashboard', {
        url: '/monkRealTimeDashboard',
        templateUrl: 'views/monkRealTimeDashboard.html',
        controller: 'monkRealTimeDashboardCtrl'
    }).state('dashboard.brandAttributes', {
        url: '/brandAttributes',
        templateUrl: 'views/brandAttributes.html',
        controller: 'brandAttributeCtrl'
    }).state('dashboard.feedbackQuestions' , {
        url: '/feedbackQuestions',
        templateUrl: 'views/feedbackQuestions.html',
        controller: 'feedbackQuestionsCtrl'
    }).state('dashboard.loyaltyManagement' , {
        url: '/loyaltyManagement',
        templateUrl: 'views/loyaltyManagement.html',
        controller: 'loyaltyManagementCtrl'
    }).state('dashboard.loyaltyReclaim' , {
        url: '/loyaltyReclaim',
        templateUrl: 'views/loyaltyReclaim.html',
        controller: 'loyaltyReclaimCtrl'
    }).state('dashboard.orderPaymentManagement' , {
        url: '/orderPaymentManagement',
        templateUrl: 'views/orderPaymentManagement.html',
        controller: 'orderPaymentManagementCtrl'
    }).state('dashboard.generateWinbackCoupon' , {
         url: '/generateWinbackCoupon',
         templateUrl: 'views/generateWinbackCoupon.html',
         controller: 'generateWinbackCouponCtrl'
    }).state('dashboard.lookupWinbackCoupon' , {
         url: '/lookupWinbackCoupon',
         templateUrl: 'views/lookupWinbackCoupon.html',
         controller: 'lookupWinbackCouponCtrl'
    }).state('dashboard.partnerDqrMapping' , {
         url: '/partnerDqrMapping',
         templateUrl: 'views/partnerDqrMapping.html',
         controller: 'partnerDqrMappingCtrl'
    }).state('dashboard.unitProductPriceSync' , {
         url: '/unitProductPriceSync',
         templateUrl: 'views/unitProductPriceSync.html',
         controller: 'unitProductPriceSyncCtrl'
    }).state('dashboard.assemblyNotification',{
       url:'/assemblyNotification',
       templateUrl:'views/assemblyNotification.html',
       controller :'assemblyNotificationController'
    }).state('dashboard.unitDroolMapping',{
        url:'/unitDroolMapping',
        templateUrl:'views/unitDroolMapping.html',
        controller :'unitDroolMappingCtrl'
    }).state('dashboard.productCheckList', {
        url: '/productCheckList',
        templateUrl: 'views/productCheckList.html',
        controller: 'productCheckListCtrl'
    }).state('dashboard.lcdMenuFileUpload', {
        url: '/lcdMenuFileUpload',
        templateUrl: 'views/LCDMenuFileUpload.html',
        controller: 'LCDMenuFileUploadController'
    }).state('dashboard.monk2Conf', {
        url: '/monk2Conf',
        templateUrl: 'views/monk2Conf.html',
        controller: 'monk2ConfCtrl'
    })


    $urlRouterProvider.otherwise('/dashboard/unit');

}]).service('fileService', function () {
    var service = this;
    service.file = null;
    service.push = function (file) {
        service.file = file;
    };
    service.getFile = function () {
        return service.file;
    };
}).service('AuthService', function () {

    var service = this;
    service.authorization = null;
    service.getAuthorization = getAuthorization;
    service.setAuthorization = function (authorization) {
        service.authorization = authorization;
    };

    function getAuthorization() {
        return service.authorization;
    }

    return service;
}).service('authInterceptor', function ($rootScope, AuthService, $location, $cookieStore, $window) {
    var service = this;
    service.request = function (config) {
        config.headers.auth = AuthService.getAuthorization();
        if(config.method=="POST" && config.data == undefined){
            config["data"] = {};
        }
        checkForSupAdmnFilter($rootScope, $window, config);
        return config;
    };
    service.responseError = function (response) {
        if (response.status === 401) {
            console.log("Response Error 401", response);
        localStorage.removeItem('userValues');
            AuthService.setAuthorization(null);
            $location.path('/login');
        }
        return response;
    };
}).config(['$httpProvider', function ($httpProvider) {
    $httpProvider.interceptors.push('authInterceptor');
}]).run(
    function ($rootScope, $location, AuthService, $cookieStore, AppUtil) {
        $rootScope.$on('$locationChangeStart', function (event, next, current) {
            $rootScope.enableScreenFilter = null;
            AppUtil.getUserValues()!= null ? AuthService.setAuthorization(AppUtil.getUserValues().jwtToken) : AuthService.setAuthorization(null);
            $rootScope.aclData = AppUtil.getAcl();
            $rootScope.userData = AppUtil.getUserData();
            // redirect to login page if not logged in and trying to access
            // a restricted page
            var restrictedPage = $.inArray($location.path(), ['/login']) === -1;
            var loggedIn = AuthService.getAuthorization() != null;
            if (restrictedPage && !loggedIn) {
                $location.path("/login");
            }
            $rootScope.showFullScreenLoader = false;
            $rootScope.showDetailLoader = false;
            $rootScope.detailLoaderMessage = null;
        });
    }).directive('fileModel', ['$parse', 'fileService', function ($parse, fileService) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            element.bind('change', function () {
                scope.$apply(function () {
                    fileService.push(element[0].files[0]);
                });
            });
        }
    };
}]).directive('stringToNumber', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModel) {
            ngModel.$parsers.push(function (value) {
                return '' + value;
            });
            ngModel.$formatters.push(function (value) {
                return parseFloat(value, 10);
            });
        }
    };
}).directive("aclMenu", ['$rootScope', function ($rootScope){
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if (aclData != null && aclData.menu != null && (aclData.menu[attributes.aclMenu] != null || aclData.menu["ADMN_ALL"] != null)) {
            element.show();
        } else {
            element.hide();
        }
    }
    return({
        link: link,
        restrict: "A"
    });
}]).directive("aclSubMenu", ['$rootScope', function ($rootScope){
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if (aclData != null && aclData.subMenu != null && (aclData.subMenu[attributes.aclSubMenu] != null || aclData.menu["ADMN_ALL"] != null)) {
            element.show();
        } else {
            element.hide();
        }
    }
    return({
        link: link,
        restrict: "A"
    });
}]).directive("aclAction", ['$rootScope', function ($rootScope){
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if (aclData != null && aclData.action != null && (aclData.action[attributes.aclAction] != null || aclData.menu["ADMN_ALL"] != null)) {
            element.show();
        } else {
            element.hide();
        }
    }
    return({
        link: link,
        restrict: "A"
    });
}]).directive("aclActionChecker", ['$rootScope', function ($rootScope){
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if (aclData != null && aclData.action != null && aclData.action[attributes.aclActionChecker] != null)  {
            element.show();
        } else {
            element.hide();
        }
    }
    return({
        link: link,
        restrict: "A"
    });
}]).directive('productView', function () {

    return {
        restrict: 'E',
        scope: {
            productList: "=productList",
            listLabel: "=listLabel"
        },
        templateUrl: 'directives/scmProductViewDirective.html'
    };
}).constant('ITERATION_STATUS', {
	INITIATED : 'INITIATED',
	CREATED : 'CREATED',
	APPROVED : 'APPROVED',
	IMPROVED : 'IMPROVED',
	ARCHIVED : 'ARCHIVED',
	DECOMISSONED : 'DECOMISSONED'
});
function CSVToArray(strData, strDelimiter) {
    strDelimiter = (strDelimiter || ",");
    var objPattern = new RegExp(("(\\" + strDelimiter + "|\\r?\\n|\\r|^)" + "(?:\"([^\"]*(?:\"\"[^\"]*)*)\"|"
        + "([^\"\\" + strDelimiter + "\\r\\n]*))"), "gi");
    var arrData = [[]];
    var arrMatches = null;
    while (arrMatches = objPattern.exec(strData)) {
        var strMatchedDelimiter = arrMatches[1];
        if (strMatchedDelimiter.length && (strMatchedDelimiter != strDelimiter)) {
            arrData.push([]);
        }
        if (arrMatches[2]) {
            var strMatchedValue = arrMatches[2].replace(new RegExp("\"\"", "g"), "\"");
        } else {
            var strMatchedValue = arrMatches[3];
        }
        arrData[arrData.length - 1].push(strMatchedValue);
    }
    return arrData;
}

function CSV2JSON(csv) {
    var array = CSVToArray(csv);
    var objArray = [];
    for (var i = 1; i < array.length; i++) {
        objArray[i - 1] = {};
        for (var k = 0; k < array[0].length && k < array[i].length; k++) {
            var key = array[0][k];
            objArray[i - 1][key] = array[i][k]
        }
    }
    var json = JSON.stringify(objArray);
    var str = json.replace(/},/g, "},\r\n");
    return JSON.parse(str);
}

function checkForSupAdmnFilter($rootScope, $window, config) {
    var supAdminFilter = $rootScope.supAdmnFilter || $window.localStorage.getItem('supAdmnFilter');
    if (supAdminFilter && supAdminFilter != "false") {
        checkForScreenFilter($rootScope, $window, config);
    }
}

function checkForScreenFilter($rootScope, $window, config) {
    if ($rootScope.enableScreenFilter) {
        if (!config.headers.bypassCompanyBrandFilter) {
            var brandId = $rootScope.loggedInBrandId || $window.localStorage.getItem('loggedInBrandId');
            if (brandId && brandId !== "null" && !config.headers.bypassBrandFilter) {
                config.headers['brandId'] = brandId;
            }
            var companyId = $rootScope.loggedInCompanyId || $window.localStorage.getItem('loggedInCompanyId');
            if (companyId && companyId !== "null"  && !config.headers.bypassCompanyFilter) {
                config.headers['companyId'] = companyId;
            }
        }
    }
}
