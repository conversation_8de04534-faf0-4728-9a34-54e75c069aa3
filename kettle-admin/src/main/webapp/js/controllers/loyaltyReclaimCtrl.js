/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("loyaltyReclaimCtrl", ['$scope', '$http', '$rootScope', 'AppUtil', function ($scope, $http, $rootScope, AppUtil) {

    $scope.init = function () {
        $scope.dataLoading = false;
        $scope.customerPhone = '';
        $scope.startDate = '';
        $scope.endDate = '';
        $scope.showReclaimForm = false;
        $scope.reclaimType = '';
        $scope.userData = AppUtil.getCurrentUser();
        $scope.loyaltyReclaimData = null;
        $scope.showResults = false;
        
        // Calculate date restrictions - ES5 compatible
        var today = new Date();
        var oneYearAgo = new Date();
        oneYearAgo.setFullYear(today.getFullYear() - 1);
        
        // Format dates as YYYY-MM-DD for HTML5 date inputs - ES5 compatible
        var padZero = function(num) {
            return (num < 10 ? '0' : '') + num;
        };
        
        $scope.todayDate = today.getFullYear() + '-' + 
                          padZero(today.getMonth() + 1) + '-' + 
                          padZero(today.getDate());
        $scope.oneYearAgoDate = oneYearAgo.getFullYear() + '-' + 
                               padZero(oneYearAgo.getMonth() + 1) + '-' + 
                               padZero(oneYearAgo.getDate());
    };

    $scope.getStartDateMin = function () {
        if ($scope.reclaimType === 'POINTS') {
            return $scope.oneYearAgoDate;
        }
        return ''; // No restriction for ALL_POINTS
    };

    $scope.getStartDateMax = function () {
        if ($scope.reclaimType === 'POINTS') {
            return $scope.todayDate;
        }
        return ''; // No restriction for ALL_POINTS
    };

    $scope.getEndDateMin = function () {
        if ($scope.startDate) {
            return $scope.startDate;
        }
        if ($scope.reclaimType === 'POINTS') {
            return $scope.oneYearAgoDate;
        }
        return '';
    };

    $scope.getEndDateMax = function () {
        if ($scope.reclaimType === 'POINTS') {
            return $scope.todayDate;
        }
        return ''; // No restriction for ALL_POINTS
    };

    $scope.onStartDateChange = function () {
        // Reset end date if it's before the new start date
        if ($scope.startDate && $scope.endDate) {
            var startDate = new Date($scope.startDate);
            var endDate = new Date($scope.endDate);
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(0, 0, 0, 0);
            
            if (endDate < startDate) {
                $scope.endDate = '';
            }
        }
    };

    $scope.onPhoneNumberChange = function () {
        // Remove any non-numeric characters and ensure it's a string
        if ($scope.customerPhone) {
            $scope.customerPhone = $scope.customerPhone.toString().replace(/[^0-9]/g, '');
        }
    };

    $scope.toggleReclaimForm = function (type) {
        $scope.reclaimType = type;
        $scope.showReclaimForm = true; 
        $scope.customerPhone = '';
        $scope.startDate = '';
        $scope.endDate = '';
    };

    $scope.closeReclaimForm = function () {
        $scope.showReclaimForm = false;
        $scope.customerPhone = '';
        $scope.startDate = '';
        $scope.endDate = '';
        $scope.reclaimType = '';
        $scope.loyaltyReclaimData = null;
        $scope.showResults = false;
    };

    $scope.processReclaim = function () {
        // Ensure customerPhone is properly bound and trimmed
        var phoneNumber = ($scope.customerPhone || '').toString().trim();

        // Debug log to check the phone number value
        console.log('Customer phone value:', $scope.customerPhone, 'Trimmed:', phoneNumber);

        // Validate required fields
        if (!phoneNumber || phoneNumber === '') {
            alert('Please enter customer phone number');
            return;
        }

        // Validate phone number format (10 digits)
        var phoneRegex = /^\d{10}$/;
        if (!phoneRegex.test(phoneNumber)) {
            alert('Please enter a valid 10-digit phone number');
            return;
        }
        
        if (!$scope.startDate) {
            alert('Please select start date');
            return;
        }
        
        if (!$scope.endDate) {
            alert('Please select end date');
            return;
        }
        
        // Validate date restrictions for POINTS type
        if ($scope.reclaimType === 'POINTS') {
            var startDate = new Date($scope.startDate);
            var endDate = new Date($scope.endDate);
            var oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
            var today = new Date();
            
            // Reset time to start of day for accurate comparison
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(0, 0, 0, 0);
            oneYearAgo.setHours(0, 0, 0, 0);
            today.setHours(0, 0, 0, 0);
            
            if (startDate < oneYearAgo || startDate > today) {
                alert('For Reclaim Points, start date must be within the past year (from ' + 
                      $scope.oneYearAgoDate + ' to ' + $scope.todayDate + ')');
                return;
            }
            
            if (endDate < startDate) {
                alert('End date cannot be earlier than start date');
                return;
            }
            
        }
        
        // Call the API to get loyalty reclaim details
        $scope.dataLoading = true;
        $scope.loyaltyReclaimData = null;
        $scope.showResults = false;
        
        var requestParams = {
            contactNumber: phoneNumber,
            startDate: $scope.startDate,
            endDate: $scope.endDate
        };

        $http({
            method: 'GET',
            url: AppUtil.restUrls.customer.getLoyaltyReclaimDetail,
            params: requestParams
        }).then(function success(response) {
            $scope.dataLoading = false;
            
            if (response.status === 200 && response.data) {
                $scope.loyaltyReclaimData = response.data;
                $scope.showResults = true;
            } else {
                alert('No loyalty reclaim data found for the given criteria.');
            }
        }, function error(response) {
            $scope.dataLoading = false;
            var errorMsg = 'Error occurred while fetching loyalty reclaim data.';
            if (response.data && response.data.message) {
                errorMsg = response.data.message;
            }
            alert(errorMsg);
            console.error('Error fetching loyalty reclaim data:', response);
        });
    };

    $scope.performReclaim = function () {
        var confirmMessage = '';
        var apiUrl = '';
        
        if ($scope.reclaimType === 'POINTS') {
            confirmMessage = 'Are you sure you want to reclaim loyalty points for this customer? This action cannot be undone.';
            apiUrl = AppUtil.restUrls.orderManagement.reclaimLoyaltyPoints;
        } else if ($scope.reclaimType === 'ALL_POINTS') {
            confirmMessage = 'WARNING: This will reclaim ALL loyalty points across the entire system. This action cannot be undone. Are you absolutely sure?';
            apiUrl = AppUtil.restUrls.orderManagement.reclaimAllLoyaltyPoints;
        }
        
        if (confirm(confirmMessage)) {
            // Double confirmation for bulk operation
            if ($scope.reclaimType === 'ALL_POINTS') {
                if (!confirm('This is your final warning. This will affect ALL customers and ALL loyalty points. Continue?')) {
                    return;
                }
            }
            
            $scope.dataLoading = true;
            
            var requestData = {
                customerPhone: ($scope.customerPhone || '').toString().trim(),
                startDate: $scope.startDate,
                endDate: $scope.endDate,
                employeeId: $scope.userData.id,
                reclaimType: $scope.reclaimType
            };

            $http({
                method: 'POST',
                url: apiUrl,
                data: requestData
            }).then(function success(response) {
                $scope.dataLoading = false;
                
                if (response.status === 200 && response.data) {
                    var successMessage = $scope.reclaimType === 'POINTS' ? 
                        'Loyalty points reclaimed successfully!' : 
                        'All loyalty points have been reclaimed successfully!';
                    alert(successMessage);
                    $scope.closeReclaimForm();
                } else {
                    var errorMessage = $scope.reclaimType === 'POINTS' ? 
                        'Failed to reclaim loyalty points. Please try again.' : 
                        'Failed to reclaim all loyalty points. Please try again.';
                    alert(errorMessage);
                }
            }, function error(response) {
                $scope.dataLoading = false;
                var errorMsg = 'Error occurred while reclaiming loyalty points.';
                if (response.data && response.data.message) {
                    errorMsg = response.data.message;
                }
                alert(errorMsg);
                console.error('Error reclaiming loyalty points:', response);
            });
        }
    };

}]);
