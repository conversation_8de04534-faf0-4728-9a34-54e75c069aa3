<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration debug="true"
  xmlns:log4j='http://jakarta.apache.org/log4j/'>
 
	<appender name="console" class="org.apache.log4j.ConsoleAppender">
	    <layout class="org.apache.log4j.PatternLayout">
		<param name="ConversionPattern" 
			value="%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n" />
	    </layout>
	</appender>
 
	<appender name="file" class="org.apache.log4j.RollingFileAppender">
	    <param name="append" value="false" />
	    <param name="maxFileSize" value="10MB" />
	    <param name="maxBackupIndex" value="10" />
	    <param name="file" value="./logs/test.log" />
	    <layout class="org.apache.log4j.PatternLayout">
		<param name="ConversionPattern" 
			value="%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n" />
	    </layout>
	</appender>
 
	<appender name="file2" class="org.apache.log4j.RollingFileAppender">
	    <param name="append" value="false" />
	    <param name="maxFileSize" value="10MB" />
	    <param name="maxBackupIndex" value="10" />
	    <param name="file" value="./unithealthlogs/test.log" />
	    <layout class="org.apache.log4j.PatternLayout">
		<param name="ConversionPattern" 
			value="%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n" />
	    </layout>
	</appender>
	
	<category name="com.stpl.tech.kettle.service.controller.UnitHealthResource" additivity="false">
	 <priority value="debug"/>
	 <appender-ref ref="file2"/>
	</category>
	
	<category name="com.stpl.tech.kettle.core.monitoring.UnitHealthCache" additivity="false">
	 <priority value="debug"/>
	 <appender-ref ref="file2"/>
	</category>
 
	<root>
		<level value="DEBUG" />
		<appender-ref ref="console" />
		<appender-ref ref="file" />
	</root>
	<logger name="org.hibernate.engine.jdbc.spi.SqlExceptionHelper" level="OFF"/>
</log4j:configuration>