<?xml version="1.0" encoding="UTF-8"?>

<!-- For assistance related to logback-translator or configuration  -->
<!-- files in general, please contact the logback user mailing list -->
<!-- at http://www.qos.ch/mailman/listinfo/logback-user             -->
<!--                                                                -->
<!-- For professional support please see                            -->
<!--    http://www.qos.ch/shop/products/professionalSupport         -->
<!--                                                                -->
<configuration>
    <!-- <appender name="ORDERCACHE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        Tests run on modern PCs show that buffering related property
        "ImmediateFlush" has negligible impact and will be ignored. 
        See http://logback.qos.ch/manual/appenders.html#RollingFileAppender
        and http://logback.qos.ch/manual/appenders.html#TimeBasedRollingPolicy
        for further documentation
        <Append>true</Append>
        <File>./logs/orderCache/orderCache.log</File>
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %X{request.id} %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/orderCache/orderCache.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender> -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %X{request.id} %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <!-- <appender name="HEALTHFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        Tests run on modern PCs show that buffering related property
        "ImmediateFlush" has negligible impact and will be ignored. 
        See http://logback.qos.ch/manual/appenders.html#RollingFileAppender
        and http://logback.qos.ch/manual/appenders.html#TimeBasedRollingPolicy
        for further documentation
        <File>./logs/health/kettlehealth.log</File>
        <Append>true</Append>
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %X{request.id} %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/health/kettlehealth.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name="EMAILFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        Tests run on modern PCs show that buffering related property
        "ImmediateFlush" has negligible impact and will be ignored. 
        See http://logback.qos.ch/manual/appenders.html#RollingFileAppender
        and http://logback.qos.ch/manual/appenders.html#TimeBasedRollingPolicy
        for further documentation
        <Append>true</Append>
        <File>./logs/email/kettleemail.log</File>
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %X{request.id} %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/email/kettleemail.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender> -->
    <!-- <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        Tests run on modern PCs show that buffering related property
        "ImmediateFlush" has negligible impact and will be ignored. 
        See http://logback.qos.ch/manual/appenders.html#RollingFileAppender
        and http://logback.qos.ch/manual/appenders.html#TimeBasedRollingPolicy
        for further documentation
        <Append>true</Append>
        <File>./logs/kettle.log</File>
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %X{request.id} %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/kettle.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender> -->
    <!-- <logger name="com.stpl.tech.kettle.core.cache.OrderInfoCache" additivity="false" level="INFO">
        <appender-ref ref="ORDERCACHE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.stpl.tech.kettle.core.monitoring.UnitHealthCache" additivity="false" level="INFO">
        <appender-ref ref="HEALTHFILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.stpl.tech.kettle.service.controller.UnitHealthResource" additivity="false" level="INFO">
        <appender-ref ref="HEALTHFILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger> -->
    <!-- <logger name="com.stpl.tech.kettle.service.controller.EmailResource" additivity="false" level="INFO">
        <appender-ref ref="EMAILFILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger> -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <!-- <appender-ref ref="FILE"/> -->
    </root>
    <logger name="org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver" level="OFF"/>
    <logger name="org.hibernate.engine.jdbc.spi.SqlExceptionHelper" level="OFF"/>
</configuration>