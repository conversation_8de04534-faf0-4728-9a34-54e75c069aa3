#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#

environment.type=LOCAL
mail.receipt.email=<EMAIL>
mail.undelivered.email=<EMAIL>
mail.dummy.customer.id=5
mail.to.email=<EMAIL>
mail.retry.count=2
mail.thread.sleep.time=60000

server.base.dir=/data/app/kettle/dev
account.verify.email.path=http://dev.accounts.chaayos.com/accounts/verifyemail.php

# jdbc.X
#Transaction Data Source
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=*********************************************************
jdbc.user=root
jdbc.pass=321in#@!

#Master Data Source
master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver  
master.jdbc.url=****************************************************************
master.jdbc.user=root
master.jdbc.pass=321in#@!

#CLM data source
clm.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
clm.jdbc.url=*****************************************************************************
clm.jdbc.user=rptusr
clm.jdbc.pass=321In#@!

#NPS Data Source
datalake.jdbc.url=*******************************************************************
icon.image.host.url=https://d1nqp92n3q8zl7.cloudfront.net/product_image/


spring.data.mongodb.database=inventory_dev
spring.data.mongodb.uri=mongodb://kettleusrmongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000

# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate

# property to decide whether to update inventory with order
inventory.track=true

#UnitHealthCheckCacheDelayTime
unit.ping.delay.time=180000
run.auto.dayclose=false
send.stockout.notification=false
unit.stockout.exclusions=12016
run.auto.report=false
run.auto.intraday.report=false
run.auto.expense.report=false
sqs.publish.queue.order=true
sqs.publish.queue.inventory=true
run.validate.filter=true
run.aclInterceptor=false
run.intraday.external.reports=false

master.cache.host.details=localhost
master.cache.host.ports=5701,5702,5703,5704
scm.consumption.url=http://localhost:8080/scm-service/rest/v1/stock-management/day-close-event
scm.get.consumption.url=http://localhost:8080/scm-service/rest/v1/stock-management/get-consumption
add.wastage.url=http://localhost:8080/scm-service/rest/v1/stock-management/kettle-wastage-event
scm.opening.check.url=http://localhost:8080/scm-service/rest/v1/stock-management/check-opening-for-cafe
verify.price.data=http://localhost:8080/scm-service/rest/v1/stock-management/verify-price-data
scm.pnl.url=http://localhost:8080/scm-service/rest/v1/stock-management/calculate-pnl
scm.day.close.url=http://localhost:8080/scm-service/rest/v1/stock-management/cancel-day-close-event

automated.feedback.sms.trigger.for.all=true
automated.feedback.sms.dinein.time.gap=2
automated.feedback.sms.delivery.time.gap=2
automated.feedback.sms.next.day.time.gap=2
automated.feedback.sms.threshold.time.gap=1260
automated.nps.sms.threshold.time.gap=180
automated.nps.sms.dinein.time.gap=2
automated.nps.sms.delivery.time.gap=2


scm.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw
access.code.expiration.time.gap=45

kettle.service.base.url=http://localhost:8080/kettle-service
kettle.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImtldHRsZSIsImVudlR5cGUiOiJTUFJPRCIsInBhc3NDb2RlIjoiQlMyMzkiLCJpYXQiOjE1NDAyNzMxMTV9.EslEr7vSXZjKa3OrtHvXNN05pWXG7nhlowrHPIokzLc
channelpartner.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IkNIQU5ORUwtUEFSVE5FUiIsImVudlR5cGUiOiJQUk9EIiwicGFzc0NvZGUiOiJNM0EyTCIsImlhdCI6MTUyODg0OTkzOH0.RbuNjXkk7fc2xs7KwgJQTQGY6milAkKKDl8M8CSKAzU

send.automated.otp.sms=true
send.automated.otp.via.ivr=true
ivr.id =211794
send.automated.welcome.sms=true
send.automated.nps.sms=false
send.automated.delivery.nps.sms=true
automated.otp.sms.hash=JdKOIyHV4AD

feedback.notification.sms=true
feedback.notification.slack=false

delivery.allotment.sdp.sms=false
delivery.reallotment.sdp.sms=false
delivery.alloted.sdp.customer.sms=false
delivery.confirmation.customer.sms=false
delivery.confirmation.sdp.no.list=01161266306,08061915125,04846171032

slack.client.id=23671280437.80816882181
slack.client.secret.key=81d9fc3b94eb18701b15b5ae26f5057e
exempt.service.tax=true

delivery.dispatch.delay.trigger.email=false
delivery.delivered.delay.trigger.email=false
delivery.delivered.delay.trigger.slack=true
delivery.dispatch.delay.time.gap=60
delivery.delivered.delay.time.gap=120
delivery.bulk.delivered.delay.time.gap=180
delivery.support.email.id=<EMAIL>
delivery.support.secondary.email.id=<EMAIL>
delivery.buffer.time=120
rider.order.limit=3
sqs.message.queue.prefix=SHIKHAR_LOCAL
refund.cutoff.date=2017-03-01
refund.flow.date=2020-10-30

webengage.base.url=https://api.webengage.com/
webengage.licence=311c5328
webengage.auth=0ca9cb50-ea71-41a3-b896-9c7bc79bbb56

env.host.ip=localhost
socket.io.port=9092

#employee meal constants
employee.meal.amount.limit=45
employee.meal.day.limit=26
employee.meal.monthly.start.date=23

raw.print.enabled=true


inventory.base.url=http://localhost:8080
scm.base.url=http://localhost:8080

amazon.s3.bucket=chaayosdevtest
order.receipt.download.base.url=https://localhost:8080/neo-service/rest/v1/
send.kiosk.order.pdf.receipt.sms=false

pubnub.publish.key=******************************************
pubnub.subscribe.key=******************************************

promotional.offer.active=true
promotional.offer.start.date=2019-07-20
promotional.offer.end.date=2019-08-31
promotional.offer.html.text=<span style="font-size: 18px; text-align:center; line-height: 30px;">Flat 35% off on <a href="https://bit.ly/oyochaayos"><img src="https://cafes.chaayos.com/oyo-logo.png" style="margin-bottom: -10px;" title="OYO" width="40px"></a> bookings | Use code: <a href="https://bit.ly/oyochaayos"><b>OYOCHA</b></a> | Valid for 2 Bookings till 31st Aug 2019 </span>


send.feedback.message.delivery.swiggy=true
ags.checksum.key=CHAAYOS@123
cash.back.start.date=2020-11-04
cash.back.end.date=2020-11-08
cash.back.card.end.date=2020-12-08
cash.back.card.start.date=2020-11-09
cash.back.percentage=50

number.minutes.refund.block=8

dinein.crm.base.url=http://************:8989/app-crm/
dinein.token=eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiUHVaZWdpRzNMazRuNllkdWl6NUxOaWVrVTRmSXlNTUZpclBsVHBFMnV6TjZuTWV0SXJZNmh4WXdVZ3c0SGtBWk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEwMDAwLCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNjA2NDgwNTc1LCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.vKyPcdsdd-FjzCHVwScqTBCHc6rMl79J2a-faxWdNi4

aws.sqs.dine.in.app.status.queue.region=ap-south-1

ordering.maxTaxAbleAmount=3000

mission.garamChai.enable=true
mission.garamChai.maxOrderValue=-1
mission.garamChai.thresholdMPR=9
mission.garamChai.brandIds=1
mission.garamChai.channelPartnerIds=3,6

free.chai.delivery.enable=true
free.chai.delivery.sms=true
free.chai.delivery.months=6
free.chai.delivery.transaction=4

app.download.link=true

amazon.s3.product.bucket=product.image.dev
crm.screen.host.url=http://d1nqp92n3q8zl7.cloudfront.net/crmapp/

facebook.accessToken=EAAK8xTuVSZAkBALDnIG490idRUQvICTgsdVeBprUYJn2MuCIDp9r66ZAYhWDh1s9oAq6fqVDDv6FFU49ZCASoCtdQuXqkZCDO7BSI3QfDJUUX3WUchiCUdW8XubV98jAXxtE4i4GvlQqZAgnjfUP5DrB331KkbWCbxgZClg6ZBRv70EcZCbJik5A
facebook.push.enable=true
facebook.push.chaayos.dinein.eventSetId=***************
facebook.push.chaayos.delivery.eventSetId=****************
facebook.push.gnt.eventSetId=***************

google.push.enable=true
api.googleads.refreshToken=1//0gMEOP0dS834UCgYIARAAGBASNwF-L9Iri0ReqXJIxKPlv_KE3U9enAWuf0wS5m8DXyRvrZgSkYvFCbmUbgf2uE7G79xux-thGj4
api.googleads.clientSecret=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
api.googleads.clientId=************-d8pa8hlttdhuhjvr7k0h80jcvfq9q8nn.apps.googleusercontent.com
api.googleads.developerToken=4MhNq0lgF2UNikNYEG09UA
# Required for manager accounts only: Specify the login customer ID used to
# authenticate API calls. This will be the customer ID of the authenticated manager account
api.googleads.loginCustomerId=**********

order.feedback.type=internal
base.path.kettle.master = http://localhost:8080/master-service
thankyou.page.base.path=http://localhost:8989/
is.show.nps.rating=true
is.show.order.feedback.rating=false
order.feedback.question=Rate your experience
nps.question=Rate your experience

subscription.product.type=3810

dinein.post.order.offer.enabled=true
delivery.post.order.offer.enabled=true
dine.post.order.offer.check.last.n.days.value=30
delivery.post.order.offer.check.last.n.days.value=30



order.receipt.cloudfront=https://d3pcv1qlege227.cloudfront.net
amazon.s3.report.bucket=dev.chaayos.report

subscription.valid.buy.n.days.value=31
offer.multiplier.factor=3

print.delivery.temp = false

membership.buy.link.base.path=https://cafes.chaayos.com/membership

dinein.order.notification.whatsapp=false

