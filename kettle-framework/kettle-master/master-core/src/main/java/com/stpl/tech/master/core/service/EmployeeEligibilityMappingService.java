package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.EmployeeEligibilityMapping;
import com.stpl.tech.master.domain.model.BulkEmployeeMappingUploadResponse;
import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingRequest;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingResponse;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingType;
import com.stpl.tech.master.domain.model.SystemStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Service interface for Employee Eligibility Mapping operations
 */
public interface EmployeeEligibilityMappingService {

    int saveEmployeeEligibilityMappingList(List<EmployeeEligibilityMappingRequest> requestList, String createdBy, AtomicInteger successCount) throws DataUpdationException;

    /**
     * Save batch mappings from UI
     * @param requestList list of mapping requests
     * @param createdBy user who created the mappings
     * @return batch save response
     * @throws DataUpdationException if save fails
     */
    BulkEmployeeMappingUploadResponse saveEmployeeEligibilityBatchMappings(List<EmployeeEligibilityMappingRequest> requestList, String createdBy) throws DataUpdationException;

    /**
     * Get mappings by employee ID
     * @param empId employee ID
     * @return list of mappings
     */
    List<EmployeeEligibilityMappingResponse> getEligibilityEligibilityMappingsByEmpId(String empId);

    View prepareEmployeeMappingExcel(List<Long> empIds) throws Exception;

    /**
     * Download template for bulk upload
     * @return Excel template view
     * @throws Exception if template generation fails
     */
    View downloadEmployeeEligibilityMappingBulkUploadTemplate() throws Exception;

    /**
     * Validate bulk upload data without saving
     * @param bulkData list of mapping requests
     * @return validation response with errors
     * @throws Exception if validation fails
     */
    BulkEmployeeMappingUploadResponse validateEmpMappingBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData) throws Exception;

    /**
     * Process bulk upload data with backend validation and save to database
     * @param bulkData list of mapping requests
     * @param createdBy user who created the mappings
     * @return bulk upload response with validation results
     * @throws Exception if processing fails
     */
    BulkEmployeeMappingUploadResponse processEmpMappingBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData, String createdBy) throws Exception;

    String buildKey(String empId, String value, EmployeeEligibilityMappingType mappingType, EligibilityType eligibilityType, SystemStatus status);

    Map<String, EmployeeEligibilityMapping> buildExistingMappingMap(
            List<EmployeeEligibilityMappingRequest> requestList);

    BulkEmployeeMappingUploadResponse updateEmployeeEligibilityMapping(List<EmployeeEligibilityMappingRequest> requestList, String createdBy) throws DataUpdationException;

    // checking for id in request list from dao
    List<EmployeeEligibilityMapping> getEligibilityEligibilityMappingsById(@NotNull(message = " Id is required ") List<Long> mappingIds);
}
