package com.stpl.tech.master.domain.model.menu.excel;


import com.stpl.tech.master.domain.model.menu.excel.sheets.CafeGroupMappingSheet;
import com.stpl.tech.master.domain.model.menu.excel.sheets.GroupPackagingMappingSheet;
import com.stpl.tech.master.domain.model.menu.excel.sheets.GroupUpsellingMappingSheet;
import com.stpl.tech.master.domain.model.menu.excel.sheets.MenuSequenceGroupMappingSheet;
import com.stpl.tech.master.domain.model.menu.excel.sheets.MenuSequenceSlotColumns;
import com.stpl.tech.master.domain.model.menu.excel.sheets.RecommendationMappingSheet;
import org.apache.poi.ss.formula.functions.T;

import java.util.Arrays;

public enum MenuExcelSheets {
    CATEFORY_SEQUENCING("CATEFORY SEQUENCING", MenuSequenceSlotColumns.class),CAFE_GROUP_MAPPING("CAFE GROUP MAPPING"
    , CafeGroupMappingSheet.class),CATEGORY_ALIAS_MAPPING("CATEGORY ALIAS MAPPING", NoClassDefFoundError.class)
    ,SWIGGY_RECOMMENDATION("SWIGGY RECOMMENDATION", RecommendationMappingSheet.class)
    ,MENU_SEQUENCE_GROUP_MAPPING("MENU SEQUENCE GROUP MAPPING", MenuSequenceGroupMappingSheet.class) ,
    GROUP_PACKAGING_MAPPING("GROUP PACKAGING MAPPING", GroupPackagingMappingSheet.class),GROUP_UPSELLING_MAPPING("GROUP UPSELLING MAPPING" ,
            GroupUpsellingMappingSheet.class)
    ,FILTER_PRODUCTS("FILTER PRODUCTS", NoClassDefFoundError.class),


    //default if no match found
    IN_VALID_SHEET_NAME("INVALID SHEET NAME", NoClassDefFoundError.class);

    private final String sheetName;

    private final Class<?> className;

    MenuExcelSheets(String sheetName  , Class<?> className){
        this.sheetName = sheetName;
        this.className = className;

    }

    public String sheetName(){
        return sheetName;
    }

    public Class<?> getLinkedClass(){return  className;}



    public static MenuExcelSheets getSheet(String sheetName){
        return Arrays.stream(MenuExcelSheets.values()).filter(sheetEnum-> sheetEnum.sheetName.
                equalsIgnoreCase(sheetName)).findFirst().orElse(MenuExcelSheets.IN_VALID_SHEET_NAME);
    }
}