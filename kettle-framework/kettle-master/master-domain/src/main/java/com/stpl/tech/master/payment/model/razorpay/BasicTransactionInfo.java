package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

public class BasicTransactionInfo implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -2888495799907965764L;
	private String campaignId;
	private String campaignDescription;
	private String currentStatus;
	private boolean smsSuccessNotification;
	private boolean emailSuccessNotification;
	private boolean smsFailureNotification;
	private boolean emailFailureNotification;

	public String getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(String campaignId) {
		this.campaignId = campaignId;
	}

	public String getCampaignDescription() {
		return campaignDescription;
	}

	public void setCampaignDescription(String campaignName) {
		this.campaignDescription = campaignName;
	}

	public String getCurrentStatus() {
		return currentStatus;
	}

	public void setCurrentStatus(String campaignStatus) {
		this.currentStatus = campaignStatus;
	}

	public boolean isSmsSuccessNotification() {
		return smsSuccessNotification;
	}

	public void setSmsSuccessNotification(boolean smsSuccessNotification) {
		this.smsSuccessNotification = smsSuccessNotification;
	}

	public boolean isEmailSuccessNotification() {
		return emailSuccessNotification;
	}

	public void setEmailSuccessNotification(boolean emailSuccessNotification) {
		this.emailSuccessNotification = emailSuccessNotification;
	}

	public boolean isSmsFailureNotification() {
		return smsFailureNotification;
	}

	public void setSmsFailureNotification(boolean smsFailureNotification) {
		this.smsFailureNotification = smsFailureNotification;
	}

	public boolean isEmailFailureNotification() {
		return emailFailureNotification;
	}

	public void setEmailFailureNotification(boolean emailFailureNotification) {
		this.emailFailureNotification = emailFailureNotification;
	}

}
