package com.stpl.tech.master.payment.model.gpay;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.builder.ToStringBuilder;

import com.stpl.tech.master.payment.model.PaymentResponse;

public class GPayPaymentStatus implements Serializable, PaymentResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2608734310437506689L;
	private String orderId;
	private String paymentStatus;
	private String partnerPaymentStatus;

	public GPayPaymentStatus() {
		super();
	}

	public GPayPaymentStatus(String orderId, String paymentStatus, String partnerPaymentStatus) {
		super();
		this.orderId = orderId;
		this.paymentStatus = paymentStatus;
		this.partnerPaymentStatus = partnerPaymentStatus;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this).append("orderId", orderId).append("paymentStatus", paymentStatus).toString();
	}

	@Override
	public Map<String, String> getPersistentAttributes() {
		Map<String, String> map = new HashMap<String, String>();
		map.put("partnerPaymentStatus", partnerPaymentStatus);
		return map;
	}

	@Override
	public String getStatus() {
		return paymentStatus;
	}

	@Override
	public String getReason() {
		return "successful";
	}

	@Override
	public String getTransactionId() {
		return orderId;
	}

	@Override
	public String getPartnerOrderId() {
		return orderId;
	}

	public String getPartnerPaymentStatus() {
		return partnerPaymentStatus;
	}

	public void setPartnerPaymentStatus(String partnerPaymentStatus) {
		this.partnerPaymentStatus = partnerPaymentStatus;
	}


}