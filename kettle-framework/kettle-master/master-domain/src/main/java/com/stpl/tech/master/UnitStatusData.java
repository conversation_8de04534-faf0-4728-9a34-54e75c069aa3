/**
 * 
 */
package com.stpl.tech.master;

import com.stpl.tech.kettle.report.metadata.model.comparator.HasId;
import com.stpl.tech.kettle.report.metadata.model.comparator.HasStatus;

/**
 * <AUTHOR>
 *
 */
public class UnitStatusData implements HasId, HasStatus {

	private int id;
	private String status;
	private boolean live;

	public UnitStatusData() {

	}

	public UnitStatusData(int id, String status, boolean live) {
		super();
		this.id = id;
		this.status = status;
		this.live = live;
	}

	public boolean isLive() {
		return live;
	}

	public void setLive(boolean live) {
		this.live = live;
	}

	public int getId() {
		return id;
	}

	public void setId(int unitId) {
		this.id = unitId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String unitStatus) {
		this.status = unitStatus;
	}

	@Override
	public String currentStatus() {
		return status;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.kettle.report.metadata.model.comparator.HasId#objectId()
	 */
	@Override
	public Object objectId() {
		return id;
	}

}
