package com.stpl.tech.master.payment.model.AGS;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentResponse;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 *  This is AGS payment server to server status received for update.
 */
public class AGSPaymentS2SStatus implements Serializable, PaymentResponse {

    private static final long serialVersionUID = -7812239176900928908L;

    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("txnId")
    private String transactionId;
    @JsonProperty("status")
    private String status;
    @JsonProperty("Terminal_ID")
    private String terminalId;
    @JsonProperty("RRN")
    private String RRN;
    @JsonProperty("Auth_Code")
    private String authCode;
    @JsonProperty("Invoice_No")
    private String invoiceNo;
    @JsonProperty("Txn_Type")
    private String transactionType;
    @JsonProperty("Amt")
    private String amount;
    @JsonProperty("Card_No")
    private String cardNo;
    @JsonProperty("Txn_Date")
    private String transactionDate;
    @JsonProperty("Txn_Time")
    private String transactionTime;
    @JsonProperty("Response_Code")
    private String responseCode;
    @JsonProperty("Additional_Data")
    private String additionalData;
    @JsonProperty("Card_Type")
    private String cardType;
    @JsonProperty("Card_Brand")
    private String cardBrand;
    @JsonProperty("Card_Flag")
    private String cardFlag;
    @JsonProperty("Checksum")
    private String checksum;

    @Override
    public Map<String, String> getPersistentAttributes() {
        HashMap<String, String> parameters = new HashMap<String, String>();
        parameters.put("Terminal_ID", invoiceNo);
        parameters.put("RRN", RRN);
        parameters.put("Auth_Code", authCode);
        parameters.put("Invoice_No", invoiceNo);
        parameters.put("Txn_Type", transactionType);
        parameters.put("Amt", amount);
        parameters.put("Card_No", cardNo);
        parameters.put("Txn_Date", transactionDate);
        parameters.put("Txn_Time", transactionTime);
        parameters.put("Response_Code", responseCode);
        parameters.put("Additional_Data", additionalData);
        parameters.put("Card_Type", cardType);
        parameters.put("Card_Flag", cardFlag);
        parameters.put("Checksum", checksum);
        return parameters;
    }

    @Override
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Override
    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @Override
    public String getStatus() {
        return status;
    }

    @Override
    public String getReason() {
        return "Failed to process in external system";
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getRRN() {
        return RRN;
    }

    public void setRRN(String RRN) {
        this.RRN = RRN;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(String transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(String additionalData) {
        this.additionalData = additionalData;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardBrand() {
        return cardBrand;
    }

    public void setCardBrand(String cardBrand) {
        this.cardBrand = cardBrand;
    }

    public String getCardFlag() {
        return cardFlag;
    }

    public void setCardFlag(String cardFlag) {
        this.cardFlag = cardFlag;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

	@Override
	public String getPartnerOrderId() {
		return orderId;
	}
}
