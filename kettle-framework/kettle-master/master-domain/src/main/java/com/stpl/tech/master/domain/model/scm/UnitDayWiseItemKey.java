package com.stpl.tech.master.domain.model.scm;

import java.io.Serializable;

public class UnitDayWiseItemKey implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8185925383310435478L;

	private int unitId;
	private int dayOfWeek;
	private int productId;
	private String dimension;

	public UnitDayWiseItemKey() {

	}

	public UnitDayWiseItemKey(int unitId, int dayOfWeek, int productId, String dimension) {
		super();
		this.unitId = unitId;
		this.dayOfWeek = dayOfWeek;
		this.productId = productId;
		this.dimension = dimension;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public int getDayOfWeek() {
		return dayOfWeek;
	}

	public void setDayOfWeek(int dayOfWeek) {
		this.dayOfWeek = dayOfWeek;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + dayOfWeek;
		result = prime * result + ((dimension == null) ? 0 : dimension.hashCode());
		result = prime * result + productId;
		result = prime * result + unitId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UnitDayWiseItemKey other = (UnitDayWiseItemKey) obj;
		if (dayOfWeek != other.dayOfWeek)
			return false;
		if (dimension == null) {
			if (other.dimension != null)
				return false;
		} else if (!dimension.equals(other.dimension))
			return false;
		if (productId != other.productId)
			return false;
		if (unitId != other.unitId)
			return false;
		return true;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

}
