/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.24 at 05:01:33 PM IST 
//

package com.stpl.tech.master.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.stpl.tech.util.AppConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <p>
 * Java class for Address complex type.
 * <p>
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="Address"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="line1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="line2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="line3" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="locality" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="zipCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contact1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contact2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="addressType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="company" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="latitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="longitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="preferredAddress" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(value = {"handler"})
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Address", propOrder = {"id", "name", "line1", "line2", "line3", "locality", "city", "state", "country",
    "zipCode", "contact1", "contact2", "addressType", "company", "latitude", "longitude", "preferredAddress"})
@Document
public class Address implements Serializable {

    @Id
    private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
     * Added to avoid a runtime error whereby the detachAll property is checked
     * for existence but not actually used.
     *//*
	private String detachAll;
*/
    /**
     *
     */
    private static final long serialVersionUID = 8075917195527246495L;
    @Field
    protected int id;
    @XmlElement(required = true, nillable = true)
    @Field
    protected String name;
    @XmlElement(required = true, nillable = true)
    @Field
    protected String landmark;
    @XmlElement(required = true)
    @Field
    protected String line1;
    @XmlElement(required = true, nillable = true)
    @Field
    protected String line2;
    @XmlElement(required = true, nillable = true)
    @Field
    protected String line3;
    @XmlElement(required = true)
    @Field
    protected String subLocality;
    @XmlElement(required = true)
    @Field
    protected String locality;
    @XmlElement(required = true)
    @Field
    protected String city;
    @XmlElement(required = true)
    @Field
    protected String state;
    @XmlElement(required = true)
    @Field
    protected String country;
    @XmlElement(required = true, nillable = true)
    @Field
    protected String zipCode;
    @XmlElement(required = true)
    @Field
    protected String contact1;
    @XmlElement(required = true, nillable = true)
    @Field
    protected String contact2;
    @XmlElement(required = true)
    @Field
    protected String addressType;
    @XmlElement(required = true, nillable = true)
    @Field
    protected String company;
    @XmlElement(required = false, nillable = true)
    @Field
    protected String latitude;
    @XmlElement(required = false, nillable = true)
    @Field
    protected String longitude;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    @Field
    protected Boolean preferredAddress;
    protected String email;
    protected String source;
    protected String sourceId;
    protected String status= AppConstants.ACTIVE;
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    /**
     * Gets the value of the id property.
     *
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     */
    public void setId(int value) {
        this.id = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getLandmark() {
        return landmark;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setLandmark(String value) {
        this.landmark = value;
    }

    /**
     * Gets the value of the line1 property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getLine1() {
        return line1;
    }

    /**
     * Sets the value of the line1 property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setLine1(String value) {
        this.line1 = value;
    }

    /**
     * Gets the value of the line2 property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getLine2() {
        return line2;
    }

    /**
     * Sets the value of the line2 property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setLine2(String value) {
        this.line2 = value;
    }

    /**
     * Gets the value of the line3 property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getLine3() {
        return line3;
    }

    /**
     * Sets the value of the line3 property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setLine3(String value) {
        this.line3 = value;
    }

    public String getSubLocality() {
        return subLocality;
    }

    public void setSubLocality(String subLocality) {
        this.subLocality = subLocality;
    }

    /**
     * Gets the value of the locality property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getLocality() {
        return locality;
    }

    /**
     * Sets the value of the locality property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setLocality(String value) {
        this.locality = value;
    }

    /**
     * Gets the value of the city property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the state property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getState() {
        return state;
    }

    /**
     * Sets the value of the state property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setState(String value) {
        this.state = value;
    }

    /**
     * Gets the value of the country property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getCountry() {
        return country;
    }

    /**
     * Sets the value of the country property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setCountry(String value) {
        this.country = value;
    }

    /**
     * Gets the value of the zipCode property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * Sets the value of the zipCode property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setZipCode(String value) {
        this.zipCode = value;
    }

    /**
     * Gets the value of the contact1 property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getContact1() {
        return contact1;
    }

    /**
     * Sets the value of the contact1 property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setContact1(String value) {
        this.contact1 = value;
    }

    /**
     * Gets the value of the contact2 property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getContact2() {
        return contact2;
    }

    /**
     * Sets the value of the contact2 property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setContact2(String value) {
        this.contact2 = value;
    }

    /**
     * Gets the value of the addressType property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getAddressType() {
        return addressType;
    }

    /**
     * Sets the value of the addressType property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setAddressType(String value) {
        this.addressType = value;
    }

    /**
     * Gets the value of the company property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getCompany() {
        return company;
    }

    /**
     * Sets the value of the company property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setCompany(String value) {
        this.company = value;
    }

    /**
     * Gets the value of the latitude property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * Sets the value of the latitude property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setLatitude(String value) {
        this.latitude = value;
    }

    /**
     * Gets the value of the longitude property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getLongitude() {
        return longitude;
    }

    /**
     * Sets the value of the longitude property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setLongitude(String value) {
        this.longitude = value;
    }

    /**
     * Gets the value of the preferredAddress property.
     *
     * @return possible object is {@link Boolean }
     *
     */
    public Boolean isPreferredAddress() {
        return preferredAddress;
    }

    /**
     * Sets the value of the preferredAddress property.
     *
     * @param value
     *            allowed object is {@link Boolean }
     *
     */
    public void setPreferredAddress(Boolean value) {
        this.preferredAddress = value;
    }

    @Override
    public String toString() {
        return line1 + (line2 != null ? ", " + line2 : "") + (locality != null ? ", " + locality : "") + (city != null ? ", " + city : "") + (state != null ? ", " + state : "") + (country != null ? ", " + country : "") +
            (zipCode != null ? (", " + zipCode) : "");
    }

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    /*public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getDetachAll() {
        return detachAll;
    }

    public void setDetachAll(String detachAll) {
        this.detachAll = detachAll;
    }
*/
    public Boolean getPreferredAddress() {
        return preferredAddress;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
