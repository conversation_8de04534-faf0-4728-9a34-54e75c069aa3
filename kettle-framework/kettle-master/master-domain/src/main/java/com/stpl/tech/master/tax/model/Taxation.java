/**
 * 
 */
package com.stpl.tech.master.tax.model;

import java.io.Serializable;
import java.math.BigDecimal;

import org.apache.commons.lang.builder.CompareToBuilder;

/**
 * <AUTHOR>
 *
 */
public class Taxation implements Comparable<Taxation>, Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5392535342942080431L;
	private String category;
	private String code;
	private String name;
	private BigDecimal percentage;
	private String type;

	public Taxation() {

	}

	/**
	 * @param category
	 * @param code
	 * @param name
	 * @param percentage
	 */
	public Taxation(String category, String code, String name, BigDecimal percentage, String type) {
		super();
		this.category = category;
		this.code = code;
		this.name = name;
		this.percentage = percentage;
		this.type = type;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public BigDecimal getPercentage() {
		return percentage;
	}

	public void setPercentage(BigDecimal percentage) {
		this.percentage = percentage;
	}

	public int compareTo(String anotherString) {
		return category.compareTo(anotherString);
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Comparable#compareTo(java.lang.Object)
	 */
	@Override
	public int compareTo(Taxation o) {
		Taxation obj = (Taxation) o;
		return new CompareToBuilder().append(this.category, obj.category).append(this.code, obj.code)
				.append(this.percentage, obj.percentage).toComparison();
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((category == null) ? 0 : category.hashCode());
		result = prime * result + ((code == null) ? 0 : code.hashCode());
		result = prime * result + ((percentage == null) ? 0 : percentage.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Taxation other = (Taxation) obj;
		if (category == null) {
			if (other.category != null)
				return false;
		} else if (!category.equals(other.category))
			return false;
		if (code == null) {
			if (other.code != null)
				return false;
		} else if (!code.equals(other.code))
			return false;
		if (percentage == null) {
			if (other.percentage != null)
				return false;
		} else if (!percentage.equals(other.percentage))
			return false;
		return true;
	}

	public String toHeaderString() {
		return code + " @ " + percentage;
	}

	@Override
	public String toString() {
		return "Taxation [category=" + category + ", code=" + code + ", percentage=" + percentage + "]";
	}

}
