//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.26 at 02:20:27 AM IST 
//

package com.stpl.tech.master.domain.model.scm;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter5;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WastageEvent", propOrder = { "wastageId", "unitId", "businessDate", "generationTime", "comment",
		"status", "linkedKettleId", "reasonCode", "items" })
public class WastageEvent {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer wastageId;
	protected int unitId;
	protected int generatedBy;
	protected Integer linkedKettleId;
	protected String linkedKettleIdType;
	protected String kettleReason;
	protected String grReason;
	protected Integer linkedGrId;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	protected Date businessDate;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	protected Date generationTime;
	@XmlElement(required = true, nillable = true)
	protected String status;
	protected List<WastageData> items;
	@XmlElement(required = true, nillable = true)
	protected String type;
	protected List<String> errors;

	/**
	 * Gets the value of the wastageId property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getWastageId() {
		return wastageId;
	}

	/**
	 * Sets the value of the wastageId property.
	 * 
	 * @param value
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setWastageId(Integer value) {
		this.wastageId = value;
	}

	/**
	 * Gets the value of the unitId property.
	 * 
	 */
	public int getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the generatedBy property.
	 * 
	 */
	public void setGeneratedBy(int value) {
		this.generatedBy = value;
	}

	/**
	 * Gets the value of the generatedBy property.
	 *
	 */
	public int getGeneratedBy() {
		return generatedBy;
	}

	/**
	 * Sets the value of the unitId property.
	 *
	 */
	public void setUnitId(int value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the businessDate property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getBusinessDate() {
		return businessDate;
	}

	/**
	 * Sets the value of the businessDate property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setBusinessDate(Date value) {
		this.businessDate = value;
	}

	/**
	 * Gets the value of the generationTime property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getGenerationTime() {
		return generationTime;
	}

	/**
	 * Sets the value of the generationTime property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setGenerationTime(Date value) {
		this.generationTime = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link StockEventStatus }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link StockEventStatus }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	public Integer getLinkedGrId() {
		return linkedGrId;
	}

	public void setLinkedGrId(Integer linkedGrId) {
		this.linkedGrId = linkedGrId;
	}

	public Integer getLinkedKettleId() {
		return linkedKettleId;
	}

	public void setLinkedKettleId(Integer linkedKettleId) {
		this.linkedKettleId = linkedKettleId;
	}

	public List<WastageData> getItems() {
		if (items == null) {
			items = new ArrayList<>();
		}
		return items;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getLinkedKettleIdType() {
		return linkedKettleIdType;
	}

	public void setLinkedKettleIdType(String linkedKettleIdType) {
		this.linkedKettleIdType = linkedKettleIdType;
	}

	public String getKettleReason() {
		return kettleReason;
	}

	public void setKettleReason(String kettleReason) {
		this.kettleReason = kettleReason;
	}

	public String getGrReason() {
		return grReason;
	}

	public void setGrReason(String grReason) {
		this.grReason = grReason;
	}

	public List<String> getErrors() {
		return errors;
	}

	public void setErrors(List<String> errors) {
		this.errors = errors;
	}
	
}
