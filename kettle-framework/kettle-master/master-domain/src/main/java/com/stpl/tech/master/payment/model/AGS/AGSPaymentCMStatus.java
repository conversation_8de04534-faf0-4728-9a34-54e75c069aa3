package com.stpl.tech.master.payment.model.AGS;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentResponse;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class AGSPaymentCMStatus implements Serializable, PaymentResponse {

    private static final long serialVersionUID = -8723153583757724359L;

    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("txnId")
    private String transactionId;
    @JsonProperty("reverseReferenceNumber")
    private String reverseReferenceNumber;
    @JsonProperty("authCode")
    private String authCode;
    @JsonProperty("batchNo")
    private String batchNo;
    @JsonProperty("invoice")
    private String invoice;
    @JsonProperty("serviceFeeAmount")
    private String serviceFeeAmount;
    @JsonProperty("card")
    private String card;
    @JsonProperty("status")
    private String status;
    @JsonProperty("cardName")
    private String cardName;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("terminalId")
    private String terminalId;
    @JsonProperty("merchantId")
    private String merchantId;
    @JsonProperty("tipAmount")
    private String tipAmount;
    @JsonProperty("applicationName")
    private String applicationName;
    @JsonProperty("AID")
    private String AID;
    @JsonProperty("TVR")
    private String TVR;
    @JsonProperty("TSI")
    private String TSI;


    @Override
    public Map<String, String> getPersistentAttributes() {
        HashMap<String, String> parameters = new HashMap<String, String>();
        parameters.put("txnId", transactionId);
        parameters.put("reverseReferenceNumber", reverseReferenceNumber);
        parameters.put("authCode", authCode);
        parameters.put("batchNo", batchNo);
        parameters.put("invoice", invoice);
        parameters.put("serviceFeeAmount", serviceFeeAmount);
        parameters.put("card", card);
        parameters.put("cardName", cardName);
        parameters.put("amount", amount);
        parameters.put("terminalId", terminalId);
        parameters.put("merchantId", merchantId);
        parameters.put("tipAmount", tipAmount);
        parameters.put("applicationName", applicationName);
        parameters.put("AID", AID);
        parameters.put("TVR", TVR);
        parameters.put("TSI", TSI);
        return parameters;
    }

    @Override
    public String getOrderId() {
        return orderId;
    }

    @Override
    public String getStatus() {
        return status;
    }

    @Override
    public String getTransactionId() {
        return transactionId;
    }

    @Override
    public String getReason() {
        return "Failed to process in external system";
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getReverseReferenceNumber() {
        return reverseReferenceNumber;
    }

    public void setReverseReferenceNumber(String reverseReferenceNumber) {
        this.reverseReferenceNumber = reverseReferenceNumber;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getInvoice() {
        return invoice;
    }

    public void setInvoice(String invoice) {
        this.invoice = invoice;
    }

    public String getServiceFeeAmount() {
        return serviceFeeAmount;
    }

    public void setServiceFeeAmount(String serviceFeeAmount) {
        this.serviceFeeAmount = serviceFeeAmount;
    }

    public String getCard() {
        return card;
    }

    public void setCard(String card) {
        this.card = card;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getTipAmount() {
        return tipAmount;
    }

    public void setTipAmount(String tipAmount) {
        this.tipAmount = tipAmount;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getAID() {
        return AID;
    }

    public void setAID(String AID) {
        this.AID = AID;
    }

    public String getTVR() {
        return TVR;
    }

    public void setTVR(String TVR) {
        this.TVR = TVR;
    }

    public String getTSI() {
        return TSI;
    }

    public void setTSI(String TSI) {
        this.TSI = TSI;
    }

	@Override
	public String getPartnerOrderId() {
		return orderId;
	}
}
