package com.stpl.tech.master.readonly.domain.model;

public class ProductDimension {

	protected int productId;
	protected String productName;
	protected String dimension;
	protected int scmProductId;
	protected String scmProductName;

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public int getScmProductId() {
		return scmProductId;
	}

	public void setScmProductId(int scmProductId) {
		this.scmProductId = scmProductId;
	}

	public String getScmProductName() {
		return scmProductName;
	}

	public void setScmProductName(String scmProductName) {
		this.scmProductName = scmProductName;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dimension == null) ? 0 : dimension.hashCode());
		result = prime * result + productId;
		result = prime * result + ((productName == null) ? 0 : productName.hashCode());
		result = prime * result + scmProductId;
		result = prime * result + ((scmProductName == null) ? 0 : scmProductName.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProductDimension other = (ProductDimension) obj;
		if (dimension == null) {
			if (other.dimension != null)
				return false;
		} else if (!dimension.equals(other.dimension))
			return false;
		if (productId != other.productId)
			return false;
		if (productName == null) {
			if (other.productName != null)
				return false;
		} else if (!productName.equals(other.productName))
			return false;
		if (scmProductId != other.scmProductId)
			return false;
		if (scmProductName == null) {
			if (other.scmProductName != null)
				return false;
		} else if (!scmProductName.equals(other.scmProductName))
			return false;
		return true;
	}

}
