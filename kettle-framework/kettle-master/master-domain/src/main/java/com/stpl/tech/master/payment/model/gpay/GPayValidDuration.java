package com.stpl.tech.master.payment.model.gpay;

import java.io.Serializable;

public class GPayValidDuration implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6767860713452839799L;
	private Integer seconds = 600;
	private Integer nanos;
	
	

	public GPayValidDuration() {
		super();
	}

	public GPayValidDuration(Integer seconds, Integer nanos) {
		super();
		this.seconds = seconds;
		this.nanos = nanos;
	}

	public Integer getSeconds() {
		return seconds;
	}

	public void setSeconds(Integer seconds) {
		this.seconds = seconds;
	}

	public Integer getNanos() {
		return nanos;
	}

	public void setNanos(Integer nanos) {
		this.nanos = nanos;
	}

}
