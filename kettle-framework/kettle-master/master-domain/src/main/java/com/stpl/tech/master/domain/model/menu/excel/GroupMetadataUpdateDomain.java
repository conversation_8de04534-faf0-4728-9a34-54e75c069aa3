package com.stpl.tech.master.domain.model.menu.excel;


import com.stpl.tech.master.domain.model.MenuType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroupMetadataUpdateDomain {
    Integer menuSequenceId;
    Integer upsellingId;

    String menuApp;
    MenuType daySlot;

    Boolean removeMenuSequenceMapping;

    Boolean removeUpsellingMapping;


}
