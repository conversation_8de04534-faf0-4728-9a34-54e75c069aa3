package com.stpl.tech.master.payment.model.paytm;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

public class PaytmCreateRequest implements Serializable, PaymentRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6835226080589837607L;
	@JsonProperty("REQUEST_TYPE")
	private String requestType;
	@JsonProperty("MID")
	private String mid;
	@JsonProperty("ORDER_ID")
	private String orderId;
	@JsonProperty("CUST_ID")
	private String customerId;
	@JsonProperty("TXN_AMOUNT")
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	private BigDecimal transactionAmount;
	@JsonProperty("CHANNEL_ID")
	private String channelId;
	@JsonProperty("INDUSTRY_TYPE_ID")
	private String industryTypeId;
	@JsonProperty("WEBSITE")
	private String website;
	@JsonProperty("CHECKSUMHASH")
	private String checksumHash;
	@JsonProperty("MOBILE_NO")
	private String contactNumber;
	@JsonProperty("EMAIL")
	private String emailId;
	@JsonIgnore
	@JsonProperty("PROMO_CAMP_ID")
	private String promoCode;
	@JsonIgnore
	@JsonProperty("ORDER_DETAILS")
	private String orderDetail;
	@JsonIgnore
	@JsonProperty("VERIFIED_BY")
	private String verifiedBy;
	@JsonIgnore
	@JsonProperty("IS_USER_VERIFIED")
	private String isUserVerified;
	@JsonProperty("CALLBACK_URL")
	private String callbackUrl;

	@JsonIgnore
	@JsonProperty("QR_CODE_ID")
	private String qrCodeId;

	@JsonIgnore
	@JsonProperty("POS_DEVICE_ID")
	private String posId;

	public PaytmCreateRequest() {

	}

	public PaytmCreateRequest(String generatedOrderId,BigDecimal paidAmount) {
		//this.contactNumber = contactNumber;
		this.customerId = "5"; //DEFAULT CUSTOMER_ID
		//this.emailId = email;
		//this.isUserVerified = customer.isContactNumberVerified() ? "YES" : "NO";
		//this.orderDetail = String.format("%s Payment for Chaayos Order %s",
		//		order.getTransactionDetail().getPaidAmount(), order.getGenerateOrderId());
		this.orderId = generatedOrderId;
		//this.promoCode = order.getOfferCode();
		//this.requestType = "DEFAULT";
		this.transactionAmount = paidAmount;
		//this.verifiedBy = "MOBILE";
	}

	public PaytmCreateRequest(String generatedOrderId, BigDecimal paidAmount, String contactNumber) {
		this.customerId = contactNumber; //Sending contact number as customerId
		this.contactNumber = contactNumber;
		this.orderId = generatedOrderId;
		this.transactionAmount = paidAmount;
	}

	public String getRequestType() {
		return requestType;
	}

	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}

	public String getIndustryTypeId() {
		return industryTypeId;
	}

	public void setIndustryTypeId(String industryTypeId) {
		this.industryTypeId = industryTypeId;
	}

	public String getWebsite() {
		return website;
	}

	public void setWebsite(String website) {
		this.website = website;
	}

	public String getChecksumHash() {
		return checksumHash;
	}

	public void setChecksumHash(String checksumHash) {
		this.checksumHash = checksumHash;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public String getPromoCode() {
		return promoCode;
	}

	public void setPromoCode(String promoCode) {
		this.promoCode = promoCode;
	}

	public String getOrderDetail() {
		return orderDetail;
	}

	public void setOrderDetail(String orderDetail) {
		this.orderDetail = orderDetail;
	}

	public String getIsUserVerified() {
		return isUserVerified;
	}

	public void setIsUserVerified(String isUserVerified) {
		this.isUserVerified = isUserVerified;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public String getVerifiedBy() {
		return verifiedBy;
	}

	public void setVerifiedBy(String verifiedBy) {
		this.verifiedBy = verifiedBy;
	}
	@JsonIgnore
	public TreeMap<String, String> getParameters() {
		TreeMap<String, String> parameters = new TreeMap<String, String>();
		parameters.put("MID", getMid());
		parameters.put("CHANNEL_ID", getChannelId());
		parameters.put("INDUSTRY_TYPE_ID", getIndustryTypeId());
		parameters.put("WEBSITE", getWebsite());
		parameters.put("ORDER_ID", getOrderId());
		BigDecimal transactionAmount = getTransactionAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
		parameters.put("TXN_AMOUNT", "" + transactionAmount);
		parameters.put("CALLBACK_URL", getCallbackUrl());
		parameters.put("CUST_ID",getCustomerId());
		parameters.put("MOBILE_NO",getContactNumber());
		//parameters.put("EMAIL",getEmailId());
		//parameters.put("REQUEST_TYPE", getRequestType());
		return parameters;
	}
	@JsonIgnore
	@Override
	public Map<String, String> getPersistentAttributes() {
		HashMap<String, String> parameters = new HashMap<String, String>();
		parameters.put("ORDER_ID", getOrderId());
		parameters.put("TXN_AMOUNT", "" + getTransactionAmount());
		parameters.put("REQ_CHECKSUMHASH", getChecksumHash());
		return parameters;
	}
	@JsonIgnore
	@Override
	public String getStatus() {
		return "INITIATED";
	}

	@Override
	public String getPartnerOrderId() {
		return null;
	}

	public String getQrCodeId() {
		return qrCodeId;
	}

	public void setQrCodeId(String qrCodeId) {
		this.qrCodeId = qrCodeId;
	}
}
