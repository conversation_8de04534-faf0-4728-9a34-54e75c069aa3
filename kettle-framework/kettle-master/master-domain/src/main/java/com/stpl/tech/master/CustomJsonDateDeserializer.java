package com.stpl.tech.master;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class CustomJsonDateDeserializer extends JsonDeserializer<Date> {
	@Override
	public Date deserialize(JsonParser jsonparser, DeserializationContext deserializationcontext)
			throws IOException, JsonProcessingException {

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String date = jsonparser.getText();
		try {
			return format.parse(date);
		} catch (ParseException e) {
			format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
			try {
				return format.parse(date);
			} catch (ParseException e2) {
				format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
				try {
					return format.parse(date);
				} catch (ParseException e3) {
					try{
						return new Date(jsonparser.getLongValue());
					} catch (Exception e4){
						throw new RuntimeException(e3);
					}
				}
			}
		}

	}

}