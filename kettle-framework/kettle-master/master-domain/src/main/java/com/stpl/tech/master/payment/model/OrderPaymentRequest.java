package com.stpl.tech.master.payment.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;

public class OrderPaymentRequest implements Serializable {

	private static final long serialVersionUID = 954742756744335900L;

	private int paymentModeId;

	private String paymentModeName;

	private String generateOrderId;

	private String redirectUrl;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	private BigDecimal paidAmount;
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	private ApplicationName paymentSource;
	private String contactNumber;
	private String customerName;
	private String cartId;
	private Integer customerId;
	private String posId;
	private String refundReason;
	private String ingenicoRequestType;
	private String paymentModeStatus;
	private String accessToken;
	private String merchantId;
	private String paytmMid;
	private String paytmTid;
	private String merchantKey;
	private String version;
	private String aggregatorId;
	private String currencyCode;
	private String desc;
	private String posAppId;
	private String posTillNo;
	private String referenceNo;
	private String storeCode;
	private  String secretKey;

	private Integer brandId;

	public int getPaymentModeId() {
		return paymentModeId;
	}

	public void setPaymentModeId(int paymentModeId) {
		this.paymentModeId = paymentModeId;
	}

	public String getGenerateOrderId() {
		return generateOrderId;
	}

	public void setGenerateOrderId(String generateOrderId) {
		this.generateOrderId = generateOrderId;
	}

	public BigDecimal getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(BigDecimal paidAmount) {
		this.paidAmount = paidAmount;
	}

	public ApplicationName getPaymentSource() {
		return paymentSource;
	}

	public void setPaymentSource(ApplicationName paymentSource) {
		this.paymentSource = paymentSource;
	}

	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}

	/**
	 * @return the contactNumber
	 */
	public String getContactNumber() {
		return contactNumber;
	}

	/**
	 * @param contactNumber the contactNumber to set
	 */
	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	/**
	 * @return the customerName
	 */
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * @param customerName the customerName to set
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	/**
	 * @return the cartId
	 */
	public String getCartId() {
		return cartId;
	}

	/**
	 * @param cartId the cartId to set
	 */
	public void setCartId(String cartId) {
		this.cartId = cartId;
	}

	/**
	 * @return the customerId
	 */
	public Integer getCustomerId() {
		return customerId;
	}

	/**
	 * @param customerId the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public String getPosId() {
		return posId;
	}

	public void setPosId(String posId) {
		this.posId = posId;
	}

	public String getPaymentModeName() {
		return paymentModeName;
	}

	public void setPaymentModeName(String paymentModeName) {
		this.paymentModeName = paymentModeName;
	}

	public String getIngenicoRequestType() {
		return ingenicoRequestType;
	}

	public void setIngenicoRequestType(String ingenicoRequestType) {
		this.ingenicoRequestType = ingenicoRequestType;
	}

	public String getPaymentModeStatus() {
		return paymentModeStatus;
	}

	public void setPaymentModeStatus(String paymentModeStatus) {
		this.paymentModeStatus = paymentModeStatus;
	}

	public String getRefundReason() {
		return refundReason;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	public String getPaytmMid() {
		return paytmMid;
	}

	public void setPaytmMid(String paytmMid) {
		this.paytmMid = paytmMid;
	}

	public String getPaytmTid() {
		return paytmTid;
	}

	public void setPaytmTid(String paytmTid) {
		this.paytmTid = paytmTid;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getMerchantKey() {
		return merchantKey;
	}

	public void setMerchantKey(String merchantKey) {
		this.merchantKey = merchantKey;
	}

	public String getAggregatorId() {
		return aggregatorId;
	}

	public void setAggregatorId(String aggregatorId) {
		this.aggregatorId = aggregatorId;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getPosAppId() {
		return posAppId;
	}

	public void setPosAppId(String posAppId) {
		this.posAppId = posAppId;
	}

	public String getPosTillNo() {
		return posTillNo;
	}

	public void setPosTillNo(String posTillNo) {
		this.posTillNo = posTillNo;
	}

	public String getReferenceNo() {
		return referenceNo;
	}

	public void setReferenceNo(String referenceNo) {
		this.referenceNo = referenceNo;
	}

	public String getStoreCode() {
		return storeCode;
	}

	public void setStoreCode(String storeCode) {
		this.storeCode = storeCode;
	}

	public String getSecretKey() {
		return secretKey;
	}

	public void setSecretKey(String secretKey) {
		this.secretKey = secretKey;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	@Override
	public String toString() {
		return "OrderPaymentRequest [paymentModeId=" + paymentModeId + ", paymentModeName=" + paymentModeName
				+ ", generateOrderId=" + generateOrderId + ", redirectUrl=" + redirectUrl + ", paidAmount=" + paidAmount
				+ ", paymentSource=" + paymentSource + ", contactNumber=" + contactNumber + ", customerName="
				+ customerName + ", cartId=" + cartId + ", customerId=" + customerId + ", posId=" + posId
				+ ", refundReason=" + refundReason + ", ingenicoRequestType=" + ingenicoRequestType
				+ ", paymentModeStatus=" + paymentModeStatus + ", accessToken=" + accessToken + "]";
	}



}
