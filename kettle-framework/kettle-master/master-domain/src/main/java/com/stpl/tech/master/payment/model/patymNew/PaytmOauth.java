package com.stpl.tech.master.payment.model.patymNew;

public class PaytmOauth {

    private String scope;
    private String access_token;
    private Long expires;
    private String error;
    private String error_description;

    public PaytmOauth() {
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public void setExpires(Long expires) {
        this.expires = expires;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getError_description() {
        return error_description;
    }

    public void setError_description(String error_description) {
        this.error_description = error_description;
    }

    public Long getExpires() {
        return expires;
    }
}
