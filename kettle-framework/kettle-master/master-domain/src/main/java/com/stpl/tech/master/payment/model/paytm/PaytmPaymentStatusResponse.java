package com.stpl.tech.master.payment.model.paytm;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "TXNID",
        "BANKTXNID",
        "ORDERID",
        "TXNAMOUNT",
        "STATUS",
        "TXNTYPE",
        "RESPCODE",
        "RESPMSG",
        "MID",
        "REFUNDAMT",
        "TXNDATE"
})
public class PaytmPaymentStatusResponse {

    @JsonProperty("TXNID")
    private String transactionId;
    @JsonProperty("BANKTXNID")
    private String bankTransactionId;
    @JsonProperty("ORDERID")
    private String orderId;
    @JsonProperty("TXNAMOUNT")
    private String transactionAmount;
    @JsonProperty("STATUS")
    private String status;
    @JsonProperty("TXNTYPE")
    private String transactionType;
    @JsonProperty("RESPCODE")
    private String responseCode;
    @JsonProperty("RESPMSG")
    private String responseMessage;
    @JsonProperty("MID")
    private String mId;
    @JsonProperty("REFUNDAMT")
    private String refundAmount;
    @JsonProperty("TXNDATE")
    private String transactionDate;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("TXNID")
    public String getTransactionId() {
        return transactionId;
    }

    @JsonProperty("TXNID")
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @JsonProperty("BANKTXNID")
    public String getBankTransactionId() {
        return bankTransactionId;
    }

    @JsonProperty("BANKTXNID")
    public void setBankTransactionId(String bankTransactionId) {
        this.bankTransactionId = bankTransactionId;
    }

    @JsonProperty("ORDERID")
    public String getOrderId() {
        return orderId;
    }

    @JsonProperty("ORDERID")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("TXNAMOUNT")
    public String getTransactionAmount() {
        return transactionAmount;
    }

    @JsonProperty("TXNAMOUNT")
    public void setTransactionAmount(String transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    @JsonProperty("STATUS")
    public String getStatus() {
        return status;
    }

    @JsonProperty("STATUS")
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("TXNTYPE")
    public String getTransactionType() {
        return transactionType;
    }

    @JsonProperty("TXNTYPE")
    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    @JsonProperty("RESPCODE")
    public String getResponseCode() {
        return responseCode;
    }

    @JsonProperty("RESPCODE")
    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    @JsonProperty("RESPMSG")
    public String getResponseMessage() {
        return responseMessage;
    }

    @JsonProperty("RESPMSG")
    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    @JsonProperty("MID")
    public String getmId() {
        return mId;
    }

    @JsonProperty("MID")
    public void setmId(String mId) {
        this.mId = mId;
    }

    @JsonProperty("REFUNDAMT")
    public String getRefundAmount() {
        return refundAmount;
    }

    @JsonProperty("REFUNDAMT")
    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    @JsonProperty("TXNDATE")
    public String getTransactionDate() {
        return transactionDate;
    }

    @JsonProperty("TXNDATE")
    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("TXNID", transactionId)
                .append("BANKTXNID", bankTransactionId)
                .append("ORDERID", orderId)
                .append("TXNAMOUNT", transactionAmount)
                .append("STATUS", status)
                .append("TXNTYPE", transactionType)
                .append("RESPCODE", responseCode)
                .append("RESPMSG", responseMessage)
                .append("MID", mId)
                .append("REFUNDAMT", refundAmount)
                .append("TXNDATE", transactionDate)
                .append("additionalProperties", additionalProperties).toString();
    }

}