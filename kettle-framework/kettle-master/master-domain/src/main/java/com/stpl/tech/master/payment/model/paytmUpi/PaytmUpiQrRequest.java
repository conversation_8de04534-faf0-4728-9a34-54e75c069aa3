package com.stpl.tech.master.payment.model.paytmUpi;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentRequest;

import java.util.HashMap;
import java.util.Map;

import java.math.BigDecimal;
import java.util.TreeMap;

public class PaytmUpiQrRequest implements PaymentRequest {

    private String mid;
    private String orderId;
    private BigDecimal amount;
    private String businessType;
    private String posId;
    private String orderDetails;
    private String invoiceDetails;
    private String contactPhoneNo;
    private Integer productId;
    private String productType;
    private String productDetails;
    private String expiryDate;
    private Boolean imageRequired;
    private String displayName;
    private Map<String, String> additionalInfo;
    private String comment;
    private Integer inventoryCount;

    @JsonProperty("CHECKSUMHASH")
    private String checkSumHash;

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getPosId() {
        return posId;
    }

    public void setPosId(String posId) {
        this.posId = posId;
    }

    public String getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(String orderDetails) {
        this.orderDetails = orderDetails;
    }

    public String getInvoiceDetails() {
        return invoiceDetails;
    }

    public void setInvoiceDetails(String invoiceDetails) {
        this.invoiceDetails = invoiceDetails;
    }

    public String getContactPhoneNo() {
        return contactPhoneNo;
    }

    public void setContactPhoneNo(String contactPhoneNo) {
        this.contactPhoneNo = contactPhoneNo;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductDetails() {
        return productDetails;
    }

    public void setProductDetails(String productDetails) {
        this.productDetails = productDetails;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Boolean getImageRequired() {
        return imageRequired;
    }

    public void setImageRequired(Boolean imageRequired) {
        this.imageRequired = imageRequired;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Map<String, String> getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(Map<String, String> additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getInventoryCount() {
        return inventoryCount;
    }

    public void setInventoryCount(Integer inventoryCount) {
        this.inventoryCount = inventoryCount;
    }

    public String getCheckSumHash() {
        return checkSumHash;
    }

    public void setCheckSumHash(String checkSumHash) {
        this.checkSumHash = checkSumHash;
    }

    @JsonIgnore
    public TreeMap<String, String> getParameters(String businessType) {
        TreeMap<String, String> parameters = new TreeMap<String, String>();
        parameters.put("MID", getMid());
        parameters.put("ORDERID", getOrderId());
        BigDecimal transactionAmount = getAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
        parameters.put("AMOUNT", "" + transactionAmount);
        parameters.put("posId", getPosId());
        parameters.put("orderDetails", getOrderDetails());
        parameters.put("invoiceDetails", getInvoiceDetails());
        parameters.put("businessType", businessType);

        return parameters;
    }

    @JsonIgnore
    @Override
    public Map<String, String> getPersistentAttributes() {
        HashMap<String, String> parameters = new HashMap<String, String>();
        parameters.put("ORDER_ID", getOrderId());
        parameters.put("TXN_AMOUNT", "" + getAmount());
        parameters.put("REQ_CHECKSUMHASH", getCheckSumHash());
        return parameters;
    }
    @JsonIgnore
    @Override
    public String getStatus() {
        return "INITIATED";
    }

    @Override
    public String getPartnerOrderId() {
        return null;
    }
}

