package com.stpl.tech.master.domain.model.menu.excel.sheets;

import com.stpl.tech.master.domain.model.menu.excel.MenuExcelSheets;
import com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil;

import java.util.Arrays;
import java.util.List;

public enum GroupUpsellingMappingSheet {


    GROUP_NAME("CAFE Group",Boolean.TRUE,Boolean.FALSE,String.class,null),
    UPSELLING_GROUP("UPSELLING GROUP",Boolean.TRUE,Boolean.FALSE,String.class,null),
    DAY_SLOT("DAY_SLOT",Boolean.TRUE,Boolean.FALSE,String.class, MenuExcelUtil.getAllDaySlots()),
    PARTNER_ID("PARTNER_ID",Boolean.TRUE,Boolean.FALSE,Integer.class,MenuExcelUtil.getAcceptablePartnerIds()),
    BRAND_ID("BRAND_ID",Boolean.TRUE,Boolean.FALSE,Integer.class,MenuExcelUtil.getAcceptableBrandIds()),
    STATUS("STATUS",Boolean.TRUE,Boolean.FALSE,String.class,MenuExcelUtil.getAcceptableStatusValues()),
    UPDATE("UPDATE",Boolean.TRUE,Boolean.FALSE,String.class,MenuExcelUtil.getAcceptableUpdateValues()),
    IN_VALID_COLUMN("",null,null,null,null);

    GroupUpsellingMappingSheet(String columnName , Boolean isMandatory ,Boolean nullable
            , Class<?> dataType , List<?> acceptedValues){
        this.columnName = columnName;
        this.isMandatory = isMandatory;
        this.nullable = nullable;
        this.dataType = dataType;
        this.acceptableValues = acceptedValues;
    }
    private String columnName;

    private Boolean isMandatory;

    private Boolean nullable;

    private Class<?> dataType;

    private List<?> acceptableValues;

    public  String getColumnName(){return this.columnName;}

    public Boolean isMandatory(){return this.isMandatory;}

    public Boolean nullable(){return  this.nullable;}

    public Class<?> getDataType(){return this.dataType;}

    public List<?> getAcceptableValues(){return acceptableValues;}

    public static GroupUpsellingMappingSheet getColumnEnum(String columnName){
        return Arrays.stream(GroupUpsellingMappingSheet.values()).filter(column -> column.columnName.equalsIgnoreCase(columnName))
                .findFirst().orElse(GroupUpsellingMappingSheet.IN_VALID_COLUMN);
    }
}
