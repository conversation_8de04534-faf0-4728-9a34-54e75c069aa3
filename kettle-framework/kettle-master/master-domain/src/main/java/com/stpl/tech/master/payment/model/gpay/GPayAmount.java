package com.stpl.tech.master.payment.model.gpay;

import java.io.Serializable;

public class GPayAmount implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5609527956902692003L;
	private String currencyCode = "INR";
	private Integer units;
	private Integer nanos = 0;

	
	public GPayAmount() {
		super();
	}

	public GPayAmount(String currencyCode, Integer units, Integer nanos) {
		super();
		this.currencyCode = currencyCode;
		this.units = units;
		this.nanos = nanos;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public Integer getUnits() {
		return units;
	}

	public void setUnits(Integer units) {
		this.units = units;
	}

	public Integer getNanos() {
		return nanos;
	}

	public void setNanos(Integer nanos) {
		this.nanos = nanos;
	}

}
