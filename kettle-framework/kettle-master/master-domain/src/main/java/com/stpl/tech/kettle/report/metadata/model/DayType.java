package com.stpl.tech.kettle.report.metadata.model;

import com.stpl.tech.util.AppConstants;
import lombok.Getter;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;

@Getter
public enum DayType {
    SUNDAY(Calendar.SUNDAY, "Sunday"),
    MONDAY(Calendar.MONDAY, "Monday"),
    TUESDAY(Calendar.TUESDAY, "Tuesday"),
    WEDNESDAY(Calendar.WEDNESDAY, "Wednesday"),
    THURSDAY(Calendar.THURSDAY, "Thursday"),
    FRIDAY(Calendar.FRIDAY, "Friday"),
    SATURDAY(Calendar.SATURDAY, "Saturday");

    private final int calendarDay;
    private final String displayName;

    DayType(int calendarDay, String displayName) {
        this.calendarDay = calendarDay;
        this.displayName = displayName;
    }

    // Lookup maps for faster reverse resolution
    private static final Map<Integer, DayType> CALENDAR_LOOKUP = new HashMap<>();

    static {
        for (DayType dayType : values()) {
            CALENDAR_LOOKUP.put(dayType.calendarDay, dayType);
        }
    }

    /**
     * Returns the current DayType using system default time zone.
     */
    public static DayType today() {
        return today(ZoneId.of(AppConstants.DEFAULT_TIME_ZONE));
    }

    /**
     * Returns the current DayType for a given ZoneId.
     */
    public static DayType today(ZoneId zoneId) {
        LocalDate date = LocalDate.now(zoneId);
        DayOfWeek dow = date.getDayOfWeek(); // MONDAY=1 ... SUNDAY=7
        int calendarDay = (dow == DayOfWeek.SUNDAY) ? Calendar.SUNDAY : dow.getValue() + 1;
        return fromCalendarDay(calendarDay);
    }

    /**
     * Converts a Calendar constant (1-7) to DayType in O(1) time.
     */
    public static DayType fromCalendarDay(int calendarDay) {
        DayType result = CALENDAR_LOOKUP.get(calendarDay);
        if (result == null) {
            throw new IllegalArgumentException("Invalid calendar day: " + calendarDay);
        }
        return result;
    }

    /**
     * Parses a string (e.g., "monday") into DayType, case-insensitive.
     */
    public static DayType fromString(String dayName) {
        if (dayName == null || dayName.trim().isEmpty()) {
            throw new IllegalArgumentException("Day name cannot be null or empty");
        }
        try {
            return DayType.valueOf(dayName.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid day name: " + dayName);
        }
    }

}
