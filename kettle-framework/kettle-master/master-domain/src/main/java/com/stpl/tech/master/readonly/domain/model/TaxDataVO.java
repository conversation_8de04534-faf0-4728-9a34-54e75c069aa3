/**
 * 
 */
package com.stpl.tech.master.readonly.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
public class TaxDataVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4305786047516427492L;

	protected String taxCode;

	protected StateTaxVO state;

	protected List<AdditionalTaxVO> others;

	public TaxDataVO() {

	}

	public TaxDataVO(com.stpl.tech.master.tax.model.TaxData p) {
		this.taxCode = p.getTaxCode();
		this.state = new StateTaxVO(p.getState());
		if (p.getOthers() != null && p.getOthers().size() > 0) {
			for (com.stpl.tech.master.tax.model.AdditionalTax t : p.getOthers()) {
				this.getOthers().add(new AdditionalTaxVO(t));
			}
		}
	}

	public StateTaxVO getState() {
		return state;
	}

	public void setState(StateTaxVO state) {
		this.state = state;
	}

	public List<AdditionalTaxVO> getOthers() {
		if (others == null) {
			others = new ArrayList<>();
		}
		return others;
	}

	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		TaxDataVO taxDataVO = (TaxDataVO) o;
		return Objects.equals(taxCode, taxDataVO.taxCode) &&
				Objects.equals(state, taxDataVO.state) &&
				Objects.equals(others, taxDataVO.others);
	}

	@Override
	public int hashCode() {

		return Objects.hash(taxCode, state, others);
	}
}
