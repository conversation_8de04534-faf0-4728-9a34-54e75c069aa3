
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "id", "ref_num", "entity", "customer_id", "customer_details", "order_id", "line_items",
		"payment_id", "status", "issued_at", "paid_at", "sms_status", "email_status", "date", "terms", "amount",
		"notes", "currency", "short_url", "view_less", "type", "created_at" })
public class InvoiceEntity implements Serializable {

	private final static long serialVersionUID = -1018165901395820374L;

	@JsonProperty("id")
	public String id;
	@JsonProperty("ref_num")
	public String refNum;
	@JsonProperty("entity")
	public String entity;
	@JsonProperty("customer_id")
	public String customerId;
	@JsonProperty("customer_details")
	public CustomerDetails customerDetails;
	@JsonProperty("order_id")
	public String orderId;
	@JsonProperty("line_items")
	public List<Object> lineItems = null;
	@JsonProperty("payment_id")
	public String paymentId;
	@JsonProperty("status")
	public String status;
	@JsonProperty("issued_at")
	public Object issuedAt;
	@JsonProperty("paid_at")
	public Integer paidAt;
	@JsonProperty("sms_status")
	public String smsStatus;
	@JsonProperty("email_status")
	public String emailStatus;
	@JsonProperty("date")
	public Object date;
	@JsonProperty("terms")
	public Object terms;
	@JsonProperty("amount")
	public Integer amount;
	@JsonProperty("notes")
	public Object notes;
	@JsonProperty("currency")
	public String currency;
	@JsonProperty("short_url")
	public String shortUrl;
	@JsonProperty("view_less")
	public Boolean viewLess;
	@JsonProperty("type")
	public String type;
	@JsonProperty("created_at")
	public long createdAt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getRefNum() {
		return refNum;
	}

	public void setRefNum(String refNum) {
		this.refNum = refNum;
	}

	public String getEntity() {
		return entity;
	}

	public void setEntity(String entity) {
		this.entity = entity;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public CustomerDetails getCustomerDetails() {
		return customerDetails;
	}

	public void setCustomerDetails(CustomerDetails customerDetails) {
		this.customerDetails = customerDetails;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public List<Object> getLineItems() {
		return lineItems;
	}

	public void setLineItems(List<Object> lineItems) {
		this.lineItems = lineItems;
	}

	public String getPaymentId() {
		return paymentId;
	}

	public void setPaymentId(String paymentId) {
		this.paymentId = paymentId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Object getIssuedAt() {
		return issuedAt;
	}

	public void setIssuedAt(Object issuedAt) {
		this.issuedAt = issuedAt;
	}

	public Integer getPaidAt() {
		return paidAt;
	}

	public void setPaidAt(Integer paidAt) {
		this.paidAt = paidAt;
	}

	public String getSmsStatus() {
		return smsStatus;
	}

	public void setSmsStatus(String smsStatus) {
		this.smsStatus = smsStatus;
	}

	public String getEmailStatus() {
		return emailStatus;
	}

	public void setEmailStatus(String emailStatus) {
		this.emailStatus = emailStatus;
	}

	public Object getDate() {
		return date;
	}

	public void setDate(Object date) {
		this.date = date;
	}

	public Object getTerms() {
		return terms;
	}

	public void setTerms(Object terms) {
		this.terms = terms;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public Object getNotes() {
		return notes;
	}

	public void setNotes(Object notes) {
		this.notes = notes;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getShortUrl() {
		return shortUrl;
	}

	public void setShortUrl(String shortUrl) {
		this.shortUrl = shortUrl;
	}

	public Boolean getViewLess() {
		return viewLess;
	}

	public void setViewLess(Boolean viewLess) {
		this.viewLess = viewLess;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public long getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(long createdAt) {
		this.createdAt = createdAt;
	}

}
