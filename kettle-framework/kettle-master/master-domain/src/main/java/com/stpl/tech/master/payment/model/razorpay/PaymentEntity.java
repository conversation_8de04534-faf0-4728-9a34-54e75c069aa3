
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "id", "entity", "amount", "currency", "status", "order_id", "invoice_id", "international",
		"method", "amount_refunded", "refund_status", "captured", "description", "card_id", "bank", "wallet", "vpa",
		"email", "contact", "notes", "fee", "service_tax", "error_code", "error_description", "created_at" })
public class PaymentEntity implements Serializable {

	private final static long serialVersionUID = 1800567678286228451L;
	@JsonProperty("id")
	public String id;
	@JsonProperty("entity")
	public String entity;
	@JsonProperty("amount")
	public Integer amount;
	@JsonProperty("currency")
	public String currency;
	@JsonProperty("status")
	public String status;
	@JsonProperty("order_id")
	public String orderId;
	@JsonProperty("invoice_id")
	public String invoiceId;
	@JsonProperty("international")
	public Boolean international;
	@JsonProperty("method")
	public String method;
	@JsonProperty("amount_refunded")
	public Integer amountRefunded;
	@JsonProperty("refund_status")
	public String refundStatus;
	@JsonProperty("captured")
	public Boolean captured;
	@JsonProperty("description")
	public String description;
	@JsonProperty("card_id")
	public String cardId;
	@JsonProperty("bank")
	public String bank;
	@JsonProperty("wallet")
	public String wallet;
	@JsonProperty("vpa")
	public String vpa;
	@JsonProperty("email")
	public String email;
	@JsonProperty("contact")
	public String contact;
	@JsonProperty("notes")
	public PaymentNotes notes;
	@JsonProperty("fee")
	public Integer fee;
	@JsonProperty("service_tax")
	public Integer serviceTax;
	@JsonProperty("tax")
	public Integer tax;
	@JsonProperty("error_code")
	public String errorCode;
	@JsonProperty("error_description")
	public String errorDescription;
	@JsonProperty("created_at")
	public long createdAt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getEntity() {
		return entity;
	}

	public void setEntity(String entity) {
		this.entity = entity;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(String invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Boolean getInternational() {
		return international;
	}

	public void setInternational(Boolean international) {
		this.international = international;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public Integer getAmountRefunded() {
		return amountRefunded;
	}

	public void setAmountRefunded(Integer amountRefunded) {
		this.amountRefunded = amountRefunded;
	}

	public String getRefundStatus() {
		return refundStatus;
	}

	public void setRefundStatus(String refundStatus) {
		this.refundStatus = refundStatus;
	}

	public Boolean getCaptured() {
		return captured;
	}

	public void setCaptured(Boolean captured) {
		this.captured = captured;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getCardId() {
		return cardId;
	}

	public void setCardId(String cardId) {
		this.cardId = cardId;
	}

	public String getBank() {
		return bank;
	}

	public void setBank(String bank) {
		this.bank = bank;
	}

	public String getWallet() {
		return wallet;
	}

	public void setWallet(String wallet) {
		this.wallet = wallet;
	}

	public String getVpa() {
		return vpa;
	}

	public void setVpa(String vpa) {
		this.vpa = vpa;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public PaymentNotes getNotes() {
		return notes;
	}

	public void setNotes(PaymentNotes notes) {
		this.notes = notes;
	}

	public Integer getFee() {
		return fee;
	}

	public void setFee(Integer fee) {
		this.fee = fee;
	}

	public Integer getServiceTax() {
		return serviceTax;
	}

	public void setServiceTax(Integer serviceTax) {
		this.serviceTax = serviceTax;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorDescription() {
		return errorDescription;
	}

	public void setErrorDescription(String errorDescription) {
		this.errorDescription = errorDescription;
	}

	public long getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(long createdAt) {
		this.createdAt = createdAt;
	}

	public Integer getTax() {
		return tax;
	}

	public void setTax(Integer tax) {
		this.tax = tax;
	}

	
}
