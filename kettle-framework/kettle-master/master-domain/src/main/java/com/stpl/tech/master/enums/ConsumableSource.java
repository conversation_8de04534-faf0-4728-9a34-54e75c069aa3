package com.stpl.tech.master.enums;


import lombok.Getter;

@Getter
public enum ConsumableSource {
    DINE_IN("DINE_IN"),
    DELIVERY("DELIVERY"),
    TAKE_AWAY("TAKE_AWAY");

    private final String value;

    ConsumableSource(String value) {
        this.value = value;
    }

    public static ConsumableSource fromValue(String value) {
        for (ConsumableSource source : values()) {
            if (source.getValue().equalsIgnoreCase(value)) {
                return source;
            }
        }
        throw new IllegalArgumentException("Unknown source: " + value);
    }
}

