package com.stpl.tech.master.payment.model.ingenico;

public class IngenicoPayment {
    IngenicoPaymentRequestMethod method;
    IngenicoPaymentInstrument instrument;
    IngenicoPaymentInstruction instruction;


    // Getter Methods

    public IngenicoPaymentRequestMethod getMethod() {
        return method;
    }

    public IngenicoPaymentInstrument getInstrument() {
        return instrument;
    }

    public IngenicoPaymentInstruction getInstruction() {
        return instruction;
    }

    // Setter Methods

    public void setMethod(IngenicoPaymentRequestMethod methodObject) {
        this.method = methodObject;
    }

    public void setInstrument(IngenicoPaymentInstrument instrumentObject) {
        this.instrument = instrumentObject;
    }

    public void setInstruction(IngenicoPaymentInstruction instructionObject) {
        this.instruction = instructionObject;
    }
}
