package com.stpl.tech.master.payment.model.paytmUpi;

import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "head",
        "body"
})
public class PaytmUpiResponse {

    @JsonProperty("head")
    private PaytmUpiQrHead paytmCreateQRResponseHead;
    @JsonProperty("body")
    private PaytmUpiQrResponse paytmCreateQRResponseBody;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("head")
    public PaytmUpiQrHead getPaytmCreateQRResponseHead() {
        return paytmCreateQRResponseHead;
    }

    @JsonProperty("head")
    public void setPaytmCreateQRResponseHead(PaytmUpiQrHead paytmCreateQRResponseHead) {
        this.paytmCreateQRResponseHead = paytmCreateQRResponseHead;
    }

    @JsonProperty("body")
    public PaytmUpiQrResponse getPaytmCreateQRResponseBody() {
        return paytmCreateQRResponseBody;
    }

    @JsonProperty("body")
    public void setPaytmCreateQRResponseBody(PaytmUpiQrResponse paytmCreateQRResponseBody) {
        this.paytmCreateQRResponseBody = paytmCreateQRResponseBody;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("paytmCreateQRResponseHead", paytmCreateQRResponseHead).append("paytmCreateQRResponseBody", paytmCreateQRResponseBody).append("additionalProperties", additionalProperties).toString();
    }

}
