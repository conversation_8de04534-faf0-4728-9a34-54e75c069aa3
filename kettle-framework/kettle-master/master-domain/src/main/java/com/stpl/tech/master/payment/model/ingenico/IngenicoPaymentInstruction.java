package com.stpl.tech.master.payment.model.ingenico;

public class IngenicoPaymentInstruction {
    private String occurrence;
    private String amount;
    private String frequency;
    private String type;
    private String description;
    private String action;
    private String limit;
    private String endDateTime;
    private String debitDay;
    private String debitFlag;
    private String identifier;
    private String reference;
    private String startDateTime;
    private String validity;


    // Getter Methods

    public String getOccurrence() {
        return occurrence;
    }

    public String getAmount() {
        return amount;
    }

    public String getFrequency() {
        return frequency;
    }

    public String getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public String getAction() {
        return action;
    }

    public String getLimit() {
        return limit;
    }

    public String getEndDateTime() {
        return endDateTime;
    }

    public String getDebitDay() {
        return debitDay;
    }

    public String getDebitFlag() {
        return debitFlag;
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getReference() {
        return reference;
    }

    public String getStartDateTime() {
        return startDateTime;
    }

    public String getValidity() {
        return validity;
    }

    // Setter Methods

    public void setOccurrence(String occurrence) {
        this.occurrence = occurrence;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setLimit(String limit) {
        this.limit = limit;
    }

    public void setEndDateTime(String endDateTime) {
        this.endDateTime = endDateTime;
    }

    public void setDebitDay(String debitDay) {
        this.debitDay = debitDay;
    }

    public void setDebitFlag(String debitFlag) {
        this.debitFlag = debitFlag;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public void setStartDateTime(String startDateTime) {
        this.startDateTime = startDateTime;
    }

    public void setValidity(String validity) {
        this.validity = validity;
    }
}
