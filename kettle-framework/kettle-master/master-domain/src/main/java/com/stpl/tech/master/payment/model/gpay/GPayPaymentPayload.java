package com.stpl.tech.master.payment.model.gpay;

import java.io.Serializable;

public class GPayPaymentPayload implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -4164581413578548781L;
	private GPayMerchantInfo merchantInfo;
	private GPayTransactionDetails transactionDetails;
	private GPayValidDuration validDuration;
	private GPayPaymentType qrType;

	public GPayMerchantInfo getMerchantInfo() {
		return merchantInfo;
	}

	public void setMerchantInfo(GPayMerchantInfo merchantInfo) {
		this.merchantInfo = merchantInfo;
	}

	public GPayTransactionDetails getTransactionDetails() {
		return transactionDetails;
	}

	public void setTransactionDetails(GPayTransactionDetails transactionDetails) {
		this.transactionDetails = transactionDetails;
	}

	public GPayValidDuration getValidDuration() {
		return validDuration;
	}

	public void setValidDuration(GPayValidDuration validDuration) {
		this.validDuration = validDuration;
	}

	public GPayPaymentType getQrType() {
		return qrType;
	}

	public void setQrType(GPayPaymentType qrType) {
		this.qrType = qrType;
	}

}
