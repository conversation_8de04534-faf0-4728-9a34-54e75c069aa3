package com.stpl.tech.master.payment.model.gpay;

public class GPayMerchantRefund {

     private GPayOriginalTransactionId   originalTransactionId;
     private GPayAmount  amount;
     private String description;
     private GPayRefundStatus refundState;
     private GPayRefundFailureStatus failureReason;

    public GPayOriginalTransactionId getOriginalTransactionId() {
        return originalTransactionId;
    }

    public void setOriginalTransactionId(GPayOriginalTransactionId originalTransactionId) {
        this.originalTransactionId = originalTransactionId;
    }

    public GPayAmount getAmount() {
        return amount;
    }

    public void setAmount(GPayAmount amount) {
        this.amount = amount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public GPayRefundStatus getRefundState() {
        return refundState;
    }

    public void setRefundState(GPayRefundStatus refundState) {
        this.refundState = refundState;
    }

    public GPayRefundFailureStatus getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(GPayRefundFailureStatus failureReason) {
        this.failureReason = failureReason;
    }
}
