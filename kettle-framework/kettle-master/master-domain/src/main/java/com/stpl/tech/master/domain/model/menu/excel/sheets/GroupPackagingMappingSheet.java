package com.stpl.tech.master.domain.model.menu.excel.sheets;

import com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil;

import java.util.Arrays;
import java.util.List;

public enum GroupPackagingMappingSheet {

    GROUP_NAME("Cafe Group",Boolean.TRUE,Boolean.TRUE,String.class,null),
    PARTNER("PARTNER",Boolean.TRUE,Boolean.TRUE,Integer.class, MenuExcelUtil.getAcceptablePartnerIds()),
    BRAND_ID("BRAND ID",Boolean.TRUE,Boolean.TRUE,Integer.class,MenuExcelUtil.getAcceptableBrandIds()),
    PACKAGING_TYPE("PACKAGING TYPE",Boolean.TRUE,Boolean.TRUE,String.class,null),
    PACKAGING_VALUE("PACKAGING VALUE",Boolean.TRUE,Boolean.TRUE,String.class,null),
    MAPPING_STATUS("MAPPING STATUS",Boolean.TRUE,Boolean.TRUE,String.class,null),
    UPDATE("UPDATE",Boolean.TRUE,Boolean.TRUE,String.class,null),
    IN_VALID_COLUMN("",null,null,null,null);

    GroupPackagingMappingSheet(String columnName , Boolean isMandatory ,Boolean nullable
            , Class<?> dataType , List<?> acceptedValues){
        this.columnName = columnName;
        this.isMandatory = isMandatory;
        this.nullable = nullable;
        this.dataType = dataType;
        this.acceptableValues = acceptedValues;
    }
    private String columnName;

    private Boolean isMandatory;

    private Boolean nullable;

    private Class<?> dataType;

    private List<?> acceptableValues;

    public  String getColumnName(){return this.columnName;}

    public Boolean isMandatory(){return this.isMandatory;}

    public Boolean nullable(){return  this.nullable;}

    public Class<?> getDataType(){return this.dataType;}

    public List<?> getAcceptableValues(){return acceptableValues;}

    public static GroupPackagingMappingSheet getColumnEnum(String columnName){
        return Arrays.stream(GroupPackagingMappingSheet.values()).filter(column -> column.columnName.equalsIgnoreCase(columnName))
                .findFirst().orElse(GroupPackagingMappingSheet.IN_VALID_COLUMN);
    }
}
