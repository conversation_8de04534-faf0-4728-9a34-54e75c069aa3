package com.stpl.tech.master.readonly.domain.model;

import java.util.Map;

public class UnitProductRecipesKeys {
    private Integer unitId;
    private Map<Integer, Map<String, Integer>> productDimensionRecipeIdMap;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Map<Integer, Map<String, Integer>> getProductDimensionRecipeIdMap() {
        return productDimensionRecipeIdMap;
    }

    public void setProductDimensionRecipeIdMap(Map<Integer, Map<String, Integer>> productDimensionRecipeIdMap) {
        this.productDimensionRecipeIdMap = productDimensionRecipeIdMap;
    }
}
