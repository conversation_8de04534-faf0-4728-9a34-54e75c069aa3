package com.stpl.tech.master.payment.model.AGS;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentRequest;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class AGSCreateRequest implements Serializable, PaymentRequest {

    private static final long serialVersionUID = -7814991407991496622L;

    @JsonProperty("receipt")
    private String orderId;

    @JsonProperty("amount")
    private int transactionAmount;

    private String status;

    @JsonProperty("id")
    private String paymentTransactionId;

    public AGSCreateRequest() {

    }

    public AGSCreateRequest(String orderId, BigDecimal paidAmount) {

        this.orderId = orderId;
        this.transactionAmount = paidAmount.intValue();
    }

    public AGSCreateRequest(AGSServiceRequest request, String transactionId) {

        this.orderId = request.getOrderId();
        this.transactionAmount = request.getTransactionAmount();
        this.paymentTransactionId =  transactionId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public int getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(int transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public Map<String, String> getPersistentAttributes() {
        Map<String, String> attributes = new HashMap<>();
        attributes.put("order_id", orderId);
        attributes.put("amount", String.valueOf(transactionAmount));
        return attributes;
    }

    @Override
    public String getStatus() {
        return this.status;
    }

    @Override
    public String getPartnerOrderId() {
        return paymentTransactionId;
    }


}
