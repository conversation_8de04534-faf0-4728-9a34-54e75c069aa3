package com.stpl.tech.master.inventory.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductQuantityData", propOrder = { "id", "q", "u", "e", "p" })
public class ProductQuantityData implements Serializable {

	private static final long serialVersionUID = 6496544487110055105L;
	private int id;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	private BigDecimal q;
	private String u;
	private Date e;
	private BigDecimal p;

	public ProductQuantityData() {

	}

	public ProductQuantityData(int productId, BigDecimal quantity, String uom) {
		super();
		this.id = productId;
		this.q = quantity;
		this.u = uom;
		this.e = null;
	}

	public ProductQuantityData(int productId, BigDecimal quantity, String uom, Date expiry, BigDecimal price) {
		super();
		this.id = productId;
		this.q = quantity;
		this.u = uom;
		this.e = expiry;
		this.p = price;
	}

	public int getId() {
		return id;
	}

	public void setId(int productId) {
		this.id = productId;
	}

	public BigDecimal getQ() {
		return q;
	}

	public void setQ(BigDecimal quantity) {
		this.q = quantity;
	}

	public String getU() {
		return u;
	}

	public void setU(String uom) {
		this.u = uom;
	}

	public void addQuantity(BigDecimal quantity) {
		if (this.q == null) {
			this.q = quantity;
		} else {
			this.q = this.q.add(quantity);
		}
	}

	public Date getE() {
		return e;
	}

	public void setE(Date e) {
		this.e = e;
	}

	public BigDecimal getP() {
		return p;
	}

	public void setP(BigDecimal p) {
		this.p = p;
	}

}
