package com.stpl.tech.master.payment.model.ingenico;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.util.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by shikhar on 16/7/19.
 */
public class IngenicoResponse implements Serializable, PaymentResponse{

    private static final long serialVersionUID = 5192786071148808749L;

    @JsonIgnore
    private PaymentStatus txnStatus;
    @JsonIgnore
    private String txnMsg;
    @JsonIgnore
    private String txnErrorMsg;
    @JsonIgnore
    private String clientTxnRef;
    @JsonIgnore
    private String tpslBankCd;
    @JsonIgnore
    private String tpslTxnId;
    @JsonIgnore
    private BigDecimal transactionAmount;
    @JsonIgnore
    private String clntRqstMeta;
    @JsonIgnore
    private Date tpslTxnTime;
    @JsonIgnore
    private BigDecimal balanceAmout;
    @JsonIgnore
    private String cardId;
    @JsonIgnore
    private String aliasName;
    @JsonIgnore
    private String bankTransactionId;
    @JsonIgnore
    private String mandateRegNo;
    @JsonIgnore
    private String refundId;


    @JsonIgnore
    private String token;
    @JsonIgnore
    private String hash;
    @JsonIgnore
    private String rawResponse;
    @JsonIgnore
    private Map<String, String> attrs;


    public IngenicoResponse(String response) {

        /*
        *  txn_status|txn_msg|txn_err_msg|clnt_txn_ref|tpsl_bank_cd|
        *  tpsl_txn_id|txn_amt|clnt_rqst_meta|tpsl_txn_time|bal_amt|
        *  card_id|alias_name|BankTransactionID|mandate_reg_no|token|hash
        */


        /*
        * sample response from ingenico successful txn
        * txn_status=0300|txn_msg=success|txn_err_msg=NA|clnt_txn_ref=***************|tpsl_bank_cd=470|tpsl_txn_id=*********|txn_amt=10.00|clnt_rqst_meta={mob:**********}|tpsl_txn_time=18-07-2019 18:22:02|tpsl_rfnd_id=NA|bal_amt=NA|rqst_token=8e03c20f-b8c3-4759-b195-a43be6b7d95a|hash=8a5018b8812caf381d528006a405fdfbeb085309
        *
        * */

        setAttrs(response);

        this.rawResponse = response;
        this.txnStatus = parseStatus(this.attrs.get("txn_status"));
        this.txnMsg = this.attrs.get("txn_msg");
        this.txnErrorMsg = this.attrs.get("txn_err_msg");
        this.clientTxnRef = this.attrs.get("clnt_txn_ref");
        this.tpslBankCd = this.attrs.get("tpsl_bank_cd");
        this.tpslTxnId = this.attrs.get("tpsl_txn_id");
        this.transactionAmount = new BigDecimal(this.attrs.get("txn_amt"));
        this.clntRqstMeta = this.attrs.get("clnt_rqst_meta");
        this.tpslTxnTime = AppUtils.getDate(this.attrs.get("tpsl_txn_time"), "dd-mm-yyyy hh:mm:ss");
        String balAmount = (this.attrs.get("bal_amt").equalsIgnoreCase("NA") ? "0" : this.attrs.get("bal_amt"));
        this.balanceAmout = new BigDecimal(balAmount);
        this.cardId = this.attrs.get("card_id");
        this.aliasName = this.attrs.get("alias_name");
        this.bankTransactionId = this.attrs.get("BankTransactionID");
        this.mandateRegNo = this.attrs.get("mandate_reg_no");
        this.token = this.attrs.get("rqst_token");
        this.hash = this.attrs.get("hash");
        this.refundId = this.attrs.get("tpsl_rfnd_id");
    }

    private void setAttrs(String response) {
        List<String> values = Arrays.asList(response.split("\\|")); // since | is a meta character
        this.attrs = new HashMap<>();
        for(String v : values){
            String[] keyValue = v.split("=");
            this.attrs.put(keyValue[0], keyValue[1]);
        }
    }

    private PaymentStatus parseStatus(String value) {
        switch (value){
            case "0300": return PaymentStatus.SUCCESSFUL;
            case "0398": return PaymentStatus.INITIATED;
            case "0399": return PaymentStatus.FAILED;
            case "0396": return PaymentStatus.CANCELLED;
            case "0400": return PaymentStatus.REFUND_PROCESSED;
            case "0499": return PaymentStatus.REFUND_FAILED;
            default: return PaymentStatus.INITIATED;
        }
    }

    public PaymentStatus getTxnStatus() {
        return txnStatus;
    }

    public void setTxnStatus(PaymentStatus txnStatus) {
        this.txnStatus = txnStatus;
    }

    public String getTxnMsg() {
        return txnMsg;
    }

    public void setTxnMsg(String txnMsg) {
        this.txnMsg = txnMsg;
    }

    public String getTxnErrorMsg() {
        return txnErrorMsg;
    }

    public void setTxnErrorMsg(String txnErrorMsg) {
        this.txnErrorMsg = txnErrorMsg;
    }

    public String getClientTxnRef() {
        return clientTxnRef;
    }

    public void setClientTxnRef(String clientTxnRef) {
        this.clientTxnRef = clientTxnRef;
    }

    public String getTpslBankCd() {
        return tpslBankCd;
    }

    public void setTpslBankCd(String tpslBankCd) {
        this.tpslBankCd = tpslBankCd;
    }

    public String getTpslTxnId() {
        return tpslTxnId;
    }

    public void setTpslTxnId(String tpslTxnId) {
        this.tpslTxnId = tpslTxnId;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getClntRqstMeta() {
        return clntRqstMeta;
    }

    public void setClntRqstMeta(String clntRqstMeta) {
        this.clntRqstMeta = clntRqstMeta;
    }

    public Date getTpslTxnTime() {
        return tpslTxnTime;
    }

    public void setTpslTxnTime(Date tpslTxnTime) {
        this.tpslTxnTime = tpslTxnTime;
    }

    public BigDecimal getBalanceAmout() {
        return balanceAmout;
    }

    public void setBalanceAmout(BigDecimal balanceAmout) {
        this.balanceAmout = balanceAmout;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public String getBankTransactionId() {
        return bankTransactionId;
    }

    public void setBankTransactionId(String bankTransactionId) {
        this.bankTransactionId = bankTransactionId;
    }

    public String getMandateRegNo() {
        return mandateRegNo;
    }

    public void setMandateRegNo(String mandateRegNo) {
        this.mandateRegNo = mandateRegNo;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getRawResponse() {
        return rawResponse;
    }

    public void setRawResponse(String rawResponse) {
        this.rawResponse = rawResponse;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    @JsonIgnore
    @Override
    public Map<String, String> getPersistentAttributes() {
        return this.attrs;
    }


    @Override
    public String getOrderId() {
        return this.clientTxnRef;
    }

    @Override
    public String getStatus() {
        return this.txnStatus.name();
    }

    @Override
    public String getReason() {
        return this.txnStatus.equals(PaymentStatus.FAILED)
                ? this.txnErrorMsg : this.txnMsg;
    }

    @Override
    public String getTransactionId() {
        return this.tpslTxnId;
    }

	@Override
	public String getPartnerOrderId() {
		return clientTxnRef;
	}
}
