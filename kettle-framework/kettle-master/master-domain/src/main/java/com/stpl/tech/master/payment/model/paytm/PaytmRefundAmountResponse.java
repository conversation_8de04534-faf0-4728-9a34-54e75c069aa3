package com.stpl.tech.master.payment.model.paytm;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "MID",
        "TXNID",
        "ORDERID",
        "TXNAMOUNT",
        "REFUNDAMOUNT",
        "TXNDATE",
        "RESPCODE",
        "RESPMSG",
        "STATUS",
        "REFID",
        "CARD_ISSUER",
        "PAYMENTMODE",
        "TOTALREFUNDAMT",
        "REFUNDDATE",
        "REFUNDTYPE",
        "REFUNDID"
})
public class PaytmRefundAmountResponse {

    @JsonProperty("MID")
    private String mid;
    @JsonProperty("TXNID")
    private String transactionId;
    @JsonProperty("ORDERID")
    private String orderId;
    @JsonProperty("TXNAMOUNT")
    private String transactionAmount;
    @JsonProperty("REFUNDAMOUNT")
    private String refundAmount;
    @JsonProperty("TXNDATE")
    private String transactionDate;
    @JsonProperty("RESPCODE")
    private String responseCode;
    @JsonProperty("RESPMSG")
    private String responseMessage;
    @JsonProperty("STATUS")
    private String status;
    @JsonProperty("REFID")
    private String referenceId;
    @JsonProperty("CARD_ISSUER")
    private String cardIssuer;
    @JsonProperty("PAYMENTMODE")
    private String paymentMode;
    @JsonProperty("TOTALREFUNDAMT")
    private String totalRefundAmount;
    @JsonProperty("REFUNDDATE")
    private String refundDate;
    @JsonProperty("REFUNDTYPE")
    private String refundType;
    @JsonProperty("REFUNDID")
    private String refundId;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("MID")
    public String getMid() {
        return mid;
    }

    @JsonProperty("MID")
    public void setMid(String mid) {
        this.mid = mid;
    }

    @JsonProperty("TXNID")
    public String getTransactionId() {
        return transactionId;
    }

    @JsonProperty("TXNID")
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @JsonProperty("ORDERID")
    public String getOrderId() {
        return orderId;
    }

    @JsonProperty("ORDERID")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("TXNAMOUNT")
    public String getTransactionAmount() {
        return transactionAmount;
    }

    @JsonProperty("TXNAMOUNT")
    public void setTransactionAmount(String transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    @JsonProperty("REFUNDAMOUNT")
    public String getRefundAmount() {
        return refundAmount;
    }

    @JsonProperty("REFUNDAMOUNT")
    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    @JsonProperty("TXNDATE")
    public String getTransactionDate() {
        return transactionDate;
    }

    @JsonProperty("TXNDATE")
    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    @JsonProperty("RESPCODE")
    public String getResponseCode() {
        return responseCode;
    }

    @JsonProperty("RESPCODE")
    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    @JsonProperty("RESPMSG")
    public String getResponseMessage() {
        return responseMessage;
    }

    @JsonProperty("RESPMSG")
    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    @JsonProperty("STATUS")
    public String getStatus() {
        return status;
    }

    @JsonProperty("STATUS")
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("REFID")
    public String getReferenceId() {
        return referenceId;
    }

    @JsonProperty("REFID")
    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    @JsonProperty("CARD_ISSUER")
    public String getCardIssuer() {
        return cardIssuer;
    }

    @JsonProperty("CARD_ISSUER")
    public void setCardIssuer(String cardIssuer) {
        this.cardIssuer = cardIssuer;
    }

    @JsonProperty("PAYMENTMODE")
    public String getPaymentMode() {
        return paymentMode;
    }

    @JsonProperty("PAYMENTMODE")
    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    @JsonProperty("TOTALREFUNDAMT")
    public String getTotalRefundAmount() {
        return totalRefundAmount;
    }

    @JsonProperty("TOTALREFUNDAMT")
    public void setTotalRefundAmount(String totalRefundAmount) {
        this.totalRefundAmount = totalRefundAmount;
    }

    @JsonProperty("REFUNDDATE")
    public String getRefundDate() {
        return refundDate;
    }

    @JsonProperty("REFUNDDATE")
    public void setRefundDate(String refundDate) {
        this.refundDate = refundDate;
    }

    @JsonProperty("REFUNDTYPE")
    public String getRefundType() {
        return refundType;
    }

    @JsonProperty("REFUNDTYPE")
    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    @JsonProperty("REFUNDID")
    public String getRefundId() {
        return refundId;
    }

    @JsonProperty("REFUNDID")
    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("MID", mid)
                .append("TXNID", transactionId)
                .append("ORDERID", orderId)
                .append("TXNAMOUNT", transactionAmount)
                .append("REFUNDAMOUNT", refundAmount)
                .append("TXNDATE", transactionDate)
                .append("RESPCODE", responseCode)
                .append("RESPMSG", responseMessage)
                .append("STATUS", status)
                .append("REFID", referenceId)
                .append("CARD_ISSUER", cardIssuer)
                .append("PAYMENTMODE", paymentMode)
                .append("TOTALREFUNDAMT", totalRefundAmount)
                .append("REFUNDDATE", refundDate)
                .append("REFUNDTYPE", refundType)
                .append("REFUNDID", refundId)
                .append("additionalProperties", additionalProperties).toString();
    }

}