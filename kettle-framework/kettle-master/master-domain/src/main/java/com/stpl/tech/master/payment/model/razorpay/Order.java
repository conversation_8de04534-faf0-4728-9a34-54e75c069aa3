
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "entity" })
public class Order implements Serializable {

	private final static long serialVersionUID = -1135459891449193029L;
	@JsonProperty("entity")
	public OrderEntity entity;

	public OrderEntity getEntity() {
		return entity;
	}

	public void setEntity(OrderEntity entity) {
		this.entity = entity;
	}

}
