package com.stpl.tech.master.payment.model.ingenico;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by shikhar on 15/7/19.
 */
public class IngenicoCreateRequest implements Serializable,PaymentRequest{


    private static final long serialVersionUID = -4515568890552013624L;

    @JsonProperty
    private String merchantId;
    @JsonProperty
    private String orderId;
    @JsonProperty
    private String customerId;
    @JsonProperty
    private BigDecimal transactionAmount;
    @JsonProperty
    private String hashToken;
    @JsonProperty
    private String contactNumber;
    @JsonProperty
    private String orderDetail;
    @JsonProperty
    private String callbackUrl;
    @JsonProperty
    private PaymentStatus paymentStatus;
    @JsonIgnore
    private String salt;


    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getHashToken() {
        return hashToken;
    }

    public void setHashToken(String hashToken) {
        this.hashToken = hashToken;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getOrderDetail() {
        return orderDetail;
    }

    public void setOrderDetail(String orderDetail) {
        this.orderDetail = orderDetail;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    @JsonIgnore
    @Override
    public String getPartnerOrderId() {
        return orderId;
    }

    @JsonIgnore
    @Override
    public Map<String, String> getPersistentAttributes() {
        Map<String, String> persistentAttrs = new HashMap<>();
        persistentAttrs.put("data", this.getData());
        persistentAttrs.put("hash", this.getHashToken());
        return persistentAttrs;
    }

    @JsonIgnore
    @Override
    public String getStatus() {
        return this.paymentStatus.name();
    }

    @JsonIgnore
    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    @JsonIgnore
    public String getData() {

        // for consumerData.merchantId|consumerData.txnId|totalamount|consumerData.accountNo|consumerData.consumerId|consumerData.consumerMobileNo|consumerData.consumerEmailId |consumerData.debitStartDate|consumerData.debitEndDate|consumerData.maxAmount|consumerData.amountType|consumerData.frequency|consumerData.cardNumber|consumerData. expMonth|consumerData.expYear|consumerData.cvvCode|SALT

        return this.getMerchantId() + "|" +
                this.getOrderId() + "|" +
                this.getTransactionAmount() + "||" +
                this.getCustomerId() + "|" +
                this.getContactNumber() + "|||||||||||" +
                this.getSalt();
    }

}
