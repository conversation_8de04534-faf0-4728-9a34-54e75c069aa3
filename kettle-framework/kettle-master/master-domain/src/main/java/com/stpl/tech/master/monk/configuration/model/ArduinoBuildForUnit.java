package com.stpl.tech.master.monk.configuration.model;

import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-01-2019.
 */
public class ArduinoBuildForUnit {

    Integer unitId;
    Map<String,String> builds;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Map<String, String> getBuilds() {
        return builds;
    }

    public void setBuilds(Map<String, String> builds) {
        this.builds = builds;
    }
}
