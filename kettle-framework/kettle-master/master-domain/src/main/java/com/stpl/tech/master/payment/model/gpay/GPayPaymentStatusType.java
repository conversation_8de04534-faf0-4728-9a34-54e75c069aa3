package com.stpl.tech.master.payment.model.gpay;

import com.stpl.tech.master.payment.model.PaymentStatus;

public enum GPayPaymentStatusType {

	PAYMENT_NOT_RECEIVED(PaymentStatus.INITIATED), PAYMENT_RECEIVED(PaymentStatus.SUCCESSFUL);

	private final PaymentStatus status;

	private GPayPaymentStatusType(PaymentStatus status) {
		this.status = status;
	}

	public PaymentStatus getStatus() {
		return status;
	}

}
