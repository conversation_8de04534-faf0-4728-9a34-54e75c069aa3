package com.stpl.tech.master.payment.model.paytmUpi;

import com.stpl.tech.master.payment.model.PaymentRequest;

import java.util.Map;

public class PaytmUpiQrResponse implements PaymentRequest {
    private String qrCodeId;
    private String qrData;
    private String image;
    private PaytmUpiResultInfo paytmUpiResultInfo;
    private String signature;

    public String getQrCodeId() {
        return qrCodeId;
    }

    public void setQrCodeId(String qrCodeId) {
        this.qrCodeId = qrCodeId;
    }

    public String getQrData() {
        return qrData;
    }

    public void setQrData(String qrData) {
        this.qrData = qrData;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public PaytmUpiResultInfo getPaytmUpiResultInfo() {
        return paytmUpiResultInfo;
    }

    public void setPaytmUpiResultInfo(PaytmUpiResultInfo paytmUpiResultInfo) {
        this.paytmUpiResultInfo = paytmUpiResultInfo;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }


    @Override
    public String getPartnerOrderId() {
        return null;
    }


    @Override
    public Map<String, String> getPersistentAttributes() {
        return null;
    }


    @Override
    public String getStatus() {
        return null;
    }
}
