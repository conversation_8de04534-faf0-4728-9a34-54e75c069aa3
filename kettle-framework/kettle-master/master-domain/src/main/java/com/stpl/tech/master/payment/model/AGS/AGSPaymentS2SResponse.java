package com.stpl.tech.master.payment.model.AGS;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 *  This is response to AGS payment server to server status update.
 */
public class AGSPaymentS2SResponse implements Serializable {

    private static final long serialVersionUID = -7415976162584100966L;

    @JsonProperty("isSaved")
    private String isSaved;

    @JsonProperty("message")
    private String message;

    public String getIsSaved() {
        return isSaved;
    }

    public void setIsSaved(String isSaved) {
        this.isSaved = isSaved;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
