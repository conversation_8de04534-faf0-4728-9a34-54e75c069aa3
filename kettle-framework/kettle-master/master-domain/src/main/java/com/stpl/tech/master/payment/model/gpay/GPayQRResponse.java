package com.stpl.tech.master.payment.model.gpay;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentResponse;

public class GPayQRResponse implements Serializable, PaymentResponse, PaymentRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1930255987674372711L;

	private String transactionId;
	private String status;
	private String qrCodeId;
	private Integer amount;
	private String accessToken;
	Map<String, String> params;
	private String partnerOrderId;

	@Override
	public Map<String, String> getPersistentAttributes() {
		Map<String, String> attributes = new HashMap<>();
		attributes.put("transactionId", transactionId);
		attributes.put("status", status);
		attributes.put("qrCodeId", qrCodeId);
		attributes.put("accessToken", accessToken);
		if (params != null && params.size() > 0) {
			attributes.putAll(params);
		}
		return attributes;
	}

	@Override
	public String getOrderId() {
		return transactionId;
	}

	@Override
	public String getStatus() {
		return status;
	}

	@Override
	public String getReason() {
		return null;
	}

	@Override
	public String getTransactionId() {
		return transactionId;
	}

	@Override
	public String getPartnerOrderId() {
		return partnerOrderId;
	}

	public String getQrCodeId() {
		return qrCodeId;
	}

	public void setQrCodeId(String qrLink) {
		this.qrCodeId = qrLink;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public void parseQRLink() {
		if (qrCodeId != null) {
			try {
				params = splitQuery(qrCodeId);
				this.partnerOrderId = params.get("tr");
			} catch (UnsupportedEncodingException | MalformedURLException e) {
			}
		}
	}

	public static void main(String[] args) throws UnsupportedEncodingException, MalformedURLException {
		Map<String, String> s = splitQuery(
				"upi://pay?pa=9599055900@okbizaxis&pn=Sunshine%20Teahouse%20Pvt%20Limited&mc=5812&aid=uGICAgIDDjcrIRQ&tr=GOOGDQBCAIBAEAQDSJRABM&am=1.00");
		System.out.println(s.size());
	}

	public static Map<String, String> splitQuery(String uri)
			throws UnsupportedEncodingException, MalformedURLException {
		uri = uri.replace("upi://pay?", "https://cafes.chaayos.com?");
		URL url = new URL(uri);
		Map<String, String> query_pairs = new LinkedHashMap<String, String>();
		String query = url.getQuery();
		String[] pairs = query.split("&");
		for (String pair : pairs) {
			int idx = pair.indexOf("=");
			query_pairs.put(URLDecoder.decode(pair.substring(0, idx), "UTF-8"),
					URLDecoder.decode(pair.substring(idx + 1), "UTF-8"));
		}
		return query_pairs;
	}

	public void setPartnerOrderId(String partnerOrderId) {
		this.partnerOrderId = partnerOrderId;
	}

	@Override
	public String toString() {
		return "GPayQRResponse [transactionId=" + transactionId + ", status=" + status + ", qrCodeId=" + qrCodeId
				+ ", amount=" + amount + ", accessToken=" + accessToken + ", params=" + params + ", partnerOrderId="
				+ partnerOrderId + "]";
	}

	
}
