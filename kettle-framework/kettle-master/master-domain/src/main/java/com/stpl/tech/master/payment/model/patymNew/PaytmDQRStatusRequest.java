package com.stpl.tech.master.payment.model.patymNew;

import com.stpl.tech.master.payment.model.PaymentRequest;
import lombok.Data;

import java.util.TreeMap;

@Data
public class PaytmDQRStatusRequest implements PaymentRequest {

    private String merchantId;
    private String orderId;
    private String merchantKey;
    private PaytmStatusResponse paytmDQRStatusResponse;

    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> checkSumMap = new TreeMap<>();
        return checkSumMap;
    }

    @Override
    public String getStatus() {
        return null;
    }
}
