package com.stpl.tech.master.payment.model.patymNew;

import com.stpl.tech.master.payment.model.PaymentRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.TreeMap;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaytmEDCStatusRequest implements PaymentRequest {
    private String paytmMid;
    private String paytmTid;
    private String merchantKey;
    private String merchantTransactionId;
    private PaytmEDCStatusResponse paytmEDCTransactionResponse;

    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> checkSumMap = new TreeMap<>();
        return checkSumMap;
    }

    @Override
    public String getStatus() {
        return null;
    }
}