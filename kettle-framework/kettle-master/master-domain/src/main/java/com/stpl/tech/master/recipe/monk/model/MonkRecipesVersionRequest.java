package com.stpl.tech.master.recipe.monk.model;

import java.util.List;


public class MonkRecipesVersionRequest {

   private List<MonkRecipesVersionRawData> rawData;
   private String region;
   private int userId;
   private String userName;

//   private String version;


   public List<MonkRecipesVersionRawData> getRawData() {
      return rawData;
   }

   public void setRawData(List<MonkRecipesVersionRawData> rawData) {
      this.rawData = rawData;
   }

   public String getRegion() {
      return region;
   }

   public void setRegion(String region) {
      this.region = region;
   }

   public int getUserId() {
      return userId;
   }

   public void setUserId(int userId) {
      this.userId = userId;
   }

   public String getUserName() {
      return userName;
   }

   public void setUserName(String userName) {
      this.userName = userName;
   }
}
