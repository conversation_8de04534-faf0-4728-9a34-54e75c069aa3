package com.stpl.tech.master.inventory.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "QuantityResponseData", propOrder = { "unitId", "details", "action" })
public class QuantityResponseData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3924727930581274302L;
	private int unitId;
	private List<ProductQuantityData> details;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	private InventoryAction action;
	private InventorySource source;
	private Integer orderId;
	private Date eventTime;

	public QuantityResponseData() {

	}

	public QuantityResponseData(int unitId, List<ProductQuantityData> details, InventoryAction action,
			InventorySource source, Integer orderId, Date eventTime) {
		super();
		this.unitId = unitId;
		this.details = details;
		this.action = action;
		this.source = source;
		this.orderId = orderId;
		this.eventTime = eventTime;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public List<ProductQuantityData> getDetails() {
		return details;
	}

	public void setDetails(List<ProductQuantityData> details) {
		this.details = details;
	}

	public InventoryAction getAction() {
		return action;
	}

	public void setAction(InventoryAction action) {
		this.action = action;
	}

	public InventorySource getSource() {
		return source;
	}

	public void setSource(InventorySource source) {
		this.source = source;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer lastOrderId) {
		this.orderId = lastOrderId;
	}

	public Date getEventTime() {
		return eventTime;
	}

	public void setEventTime(Date eventTime) {
		this.eventTime = eventTime;
	}

}