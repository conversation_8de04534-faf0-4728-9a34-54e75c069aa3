package com.stpl.tech.master.payment.model.patymNew;

import java.math.BigDecimal;

public class PaytmTransactionAmount {

    private BigDecimal value;

    private String currency;

    public PaytmTransactionAmount(BigDecimal value, String currency) {
        this.value = value;
        this.currency = currency;
    }

    public PaytmTransactionAmount() {
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
