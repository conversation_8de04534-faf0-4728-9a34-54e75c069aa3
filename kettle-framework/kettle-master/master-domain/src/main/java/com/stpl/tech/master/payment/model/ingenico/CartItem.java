package com.stpl.tech.master.payment.model.ingenico;

import java.math.BigDecimal;

public class CartItem {
    public String description;
    public String providerIdentifier;
    public String surchargeOrDiscountAmount;
    public BigDecimal amount;
    public BigDecimal comAmt;
    public String sKU;
    public String reference;
    public String identifier;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getProviderIdentifier() {
        return providerIdentifier;
    }

    public void setProviderIdentifier(String providerIdentifier) {
        this.providerIdentifier = providerIdentifier;
    }

    public String getSurchargeOrDiscountAmount() {
        return surchargeOrDiscountAmount;
    }

    public void setSurchargeOrDiscountAmount(String surchargeOrDiscountAmount) {
        this.surchargeOrDiscountAmount = surchargeOrDiscountAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getComAmt() {
        return comAmt;
    }

    public void setComAmt(BigDecimal comAmt) {
        this.comAmt = comAmt;
    }

    public String getsKU() {
        return sKU;
    }

    public void setsKU(String sKU) {
        this.sKU = sKU;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }
}
