package com.stpl.tech.master;

public class Counter {

	private int c; // local counter variable

	public Counter(int i) { // sets counter to 0
		this.c = i;

	}

	/*
	 * public Counter(Counter counter){ //second constructor that allows us to
	 * set the count
	 * 
	 * }
	 */
	public Counter increment() {
		this.c++;
		return this;
	}

	public Counter decrement() {
		this.c--;
		return this;
	}

	public String toString() {
		return c + "";
	}

	public int getC() {
		return c;
	}

	public void setC(int c) {
		this.c = c;
	}

}