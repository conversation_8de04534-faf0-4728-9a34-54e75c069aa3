package com.stpl.tech.master.domain.model.scm;

import java.io.Serializable;
import java.math.BigDecimal;

public class UnitDayWiseItemData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8185925383310435478L;

	private UnitDayWiseItemKey key;
	private BigDecimal quantity;
	private BigDecimal dineInQuantity;
	private BigDecimal deliveryQuantity;
	private BigDecimal takeawayQuantity;

	public UnitDayWiseItemData() {

	}

	public UnitDayWiseItemData(UnitDayWiseItemKey key, BigDecimal quantity, BigDecimal dineInQuantity,
			BigDecimal deliveryQuantity, BigDecimal takeawayQuantity) {
		super();
		this.key = key;
		this.quantity = quantity;
		this.dineInQuantity = dineInQuantity;
		this.deliveryQuantity = deliveryQuantity;
		this.takeawayQuantity = takeawayQuantity;
	}

	public UnitDayWiseItemKey getKey() {
		return key;
	}

	public void setKey(UnitDayWiseItemKey key) {
		this.key = key;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getDineInQuantity() {
		return dineInQuantity;
	}

	public void setDineInQuantity(BigDecimal dineInQuantity) {
		this.dineInQuantity = dineInQuantity;
	}

	public BigDecimal getDeliveryQuantity() {
		return deliveryQuantity;
	}

	public void setDeliveryQuantity(BigDecimal deliveryQuantity) {
		this.deliveryQuantity = deliveryQuantity;
	}

	public BigDecimal getTakeawayQuantity() {
		return takeawayQuantity;
	}

	public void setTakeawayQuantity(BigDecimal takeawayQuantity) {
		this.takeawayQuantity = takeawayQuantity;
	}

}
