package com.stpl.tech.master.payment.model.paytm;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;

import java.util.Map;

public class PaytmCreateRequestResponseWrapper implements PaymentRequest {

    private PaytmCreateRequest paytmCreateRequest;

    private PaytmParamResponse paytmParamResponse;

    public PaytmCreateRequestResponseWrapper() {
    }

    @Override
    public String getPartnerOrderId() {
        return null;
    }


    @Override
    public Map<String, String> getPersistentAttributes() {
        return null;
    }

    // TODO abhishek sirohi
    @Override
    public String getStatus() {
        return null;
    }

    public PaytmCreateRequestResponseWrapper(PaytmCreateRequest paytmCreateRequest, PaytmParamResponse paytmParamResponse) {
        this.paytmCreateRequest = paytmCreateRequest;
        this.paytmParamResponse = paytmParamResponse;
    }

    public PaytmCreateRequest getPaytmCreateRequest() {
        return paytmCreateRequest;
    }

    public void setPaytmCreateRequest(PaytmCreateRequest paytmCreateRequest) {
        this.paytmCreateRequest = paytmCreateRequest;
    }

    public PaytmParamResponse getPaytmParamResponse() {
        return paytmParamResponse;
    }

    public void setPaytmParamResponse(PaytmParamResponse paytmParamResponse) {
        this.paytmParamResponse = paytmParamResponse;
    }
}
