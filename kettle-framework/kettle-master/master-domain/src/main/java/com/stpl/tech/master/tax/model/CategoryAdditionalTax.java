/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 12:10:26 PM IST 
//

package com.stpl.tech.master.tax.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;

import com.stpl.tech.master.domain.model.IdCodeName;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CategoryAdditionalTax", propOrder = { "category", "taxType", "taxes", "employeeName", "employeeId",
		"country" })
@Document
public class CategoryAdditionalTax implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7409359266056304225L;

	protected String employeeName;

	protected int employeeId;

	protected IdCodeName country;

	protected IdCodeName category;

	protected IdCodeName taxType;

	protected List<AdditionalTax> taxes;

	public IdCodeName getCategory() {
		return category;
	}

	public void setCategory(IdCodeName category) {
		this.category = category;
	}

	public IdCodeName getCountry() {
		return country;
	}

	public void setCountry(IdCodeName country) {
		this.country = country;
	}

	public IdCodeName getTaxType() {
		return taxType;
	}

	public void setTaxType(IdCodeName taxType) {
		this.taxType = taxType;
	}

	public List<AdditionalTax> getTaxes() {
		if (taxes == null) {
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

}
