package com.stpl.tech.master.payment.model.patymNew;

public class PaytmParamBody {

    private String requestType;

    private String mid;

    private String websiteName;

    private String orderId;

    private String callbackUrl;

    private PaytmTransactionAmount txnAmount;

    private PaytmUserInfo userInfo;

    private String paytmSsoToken;

    public PaytmParamBody(String requestType, String mid, String websiteName, String orderId, String callbackUrl,
                          PaytmTransactionAmount txnAmount, PaytmUserInfo userInfo, String paytmSsoToken) {
        this.requestType = requestType;
        this.mid = mid;
        this.websiteName = websiteName;
        this.orderId = orderId;
        this.callbackUrl = callbackUrl;
        this.txnAmount = txnAmount;
        this.userInfo = userInfo;
        this.paytmSsoToken = paytmSsoToken;
    }

    public PaytmParamBody() {
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getWebsiteName() {
        return websiteName;
    }

    public void setWebsiteName(String websiteName) {
        this.websiteName = websiteName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public PaytmTransactionAmount getTxnAmount() {
        return txnAmount;
    }

    public void setTxnAmount(PaytmTransactionAmount txnAmount) {
        this.txnAmount = txnAmount;
    }

    public PaytmUserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(PaytmUserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public String getPaytmSsoToken() {
        return paytmSsoToken;
    }

    public void setPaytmSsoToken(String paytmSsoToken) {
        this.paytmSsoToken = paytmSsoToken;
    }
}
