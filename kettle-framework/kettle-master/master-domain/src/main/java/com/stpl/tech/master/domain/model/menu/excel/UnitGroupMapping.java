package com.stpl.tech.master.domain.model.menu.excel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UnitGroupMapping {

    private Integer groupId;
    private String groupName;
    private List<Integer> unitIds;
    private Integer priority;
    private Date creationTime;

    private Integer createdBy;

    private Date updationTime;

    private Integer updatedBy;

    private String status;
}
