package com.stpl.tech.master.monk.configuration.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 10-01-2019.
 */
@Document(collection = "arduinoBuilds")
public class ArduinoBuildData {

    @Id
    private String id;
    private Date initiationTime;
    private Integer uploadedBy;
    private String status;
    private Date activationTime;
    private Date deactivationTime;
    private String version;
    private List<UnitArduinoBuild> builds;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getInitiationTime() {
        return initiationTime;
    }

    public void setInitiationTime(Date initiationTime) {
        this.initiationTime = initiationTime;
    }

    public Integer getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(Integer uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public void setActivationTime(Date activationTime) {
        this.activationTime = activationTime;
    }

    public Date getActivationTime() {
        return activationTime;
    }

    public void setDeactivationTime(Date deactivationTime) {
        this.deactivationTime = deactivationTime;
    }

    public Date getDeactivationTime() {
        return deactivationTime;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getVersion() {
        return version;
    }

    public List<UnitArduinoBuild> getBuilds() {
        if(this.builds==null){
            this.builds = new ArrayList<>();
        }
        return builds;
    }
}
