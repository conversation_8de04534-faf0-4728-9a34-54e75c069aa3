//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.12 at 05:35:56 PM IST 
//


package com.stpl.tech.master.monk.configuration.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for MonkConfigurationType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="MonkConfigurationType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="MONK_ENABLED"/&gt;
 *     &lt;enumeration value="HOT_COLD_MERGED"/&gt;
 *     &lt;enumeration value="NUMBER_OF_MONKS"/&gt;
 *     &lt;enumeration value="MAC_ADDRESS"/&gt;
 *     &lt;enumeration value="WEIGHT_COEFFICIENT"/&gt;
 *     &lt;enumeration value="WATER_DENSITY"/&gt;
 *     &lt;enumeration value="MILK_DENSITY"/&gt;
 *     &lt;enumeration value="SMALL_PAN_WEIGHT"/&gt;
 *     &lt;enumeration value="BIG_PAN_WEIGHT"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "MonkConfigurationType")
@XmlEnum
public enum MonkConfigurationType {

    @XmlEnumValue("MONK_ENABLED")
    MONK_ENABLED("MONK_ENABLED"),
    @XmlEnumValue("VERSION")
    VERSION("VERSION"),
    @XmlEnumValue("PUBLIC_IP")
    PUBLIC_IP("PUBLIC_IP"),
    @XmlEnumValue("HOT_COLD_MERGED")
    HOT_COLD_MERGED("HOT_COLD_MERGED"),
    @XmlEnumValue("NUMBER_OF_MONKS")
    NUMBER_OF_MONKS("NUMBER_OF_MONKS"),
    @XmlEnumValue("MAC_ADDRESS")
    MAC_ADDRESS("MAC_ADDRESS"),
    @XmlEnumValue("WEIGHT_COEFFICIENT")
    WEIGHT_COEFFICIENT("WEIGHT_COEFFICIENT"),
    @XmlEnumValue("WATER_DENSITY")
    WATER_DENSITY("WATER_DENSITY"),
    @XmlEnumValue("MILK_DENSITY")
    MILK_DENSITY("MILK_DENSITY"),
    @XmlEnumValue("SMALL_PAN_WEIGHT")
    SMALL_PAN_WEIGHT("SMALL_PAN_WEIGHT"),
    @XmlEnumValue("BIG_PAN_WEIGHT")
    BIG_PAN_WEIGHT("BIG_PAN_WEIGHT");

    private final String value;

    MonkConfigurationType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static MonkConfigurationType fromValue(String v) {
        for (MonkConfigurationType c: MonkConfigurationType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
