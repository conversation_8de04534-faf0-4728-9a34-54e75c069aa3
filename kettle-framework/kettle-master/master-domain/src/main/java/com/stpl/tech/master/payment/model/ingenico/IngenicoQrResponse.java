package com.stpl.tech.master.payment.model.ingenico;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentResponse;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


public class IngenicoQrResponse implements PaymentResponse, PaymentRequest {
    private String merchantCode;
    private String merchantTransactionIdentifier;
    private String merchantTransactionRequestType;
    private String responseType;
    private String transactionState;
    private String merchantAdditionalDetails;
    IngenicoPaymentMethod paymentMethod;
    private String error = null;

    // Getter Methods

    public String getMerchantCode() {
        return merchantCode;
    }

    public String getMerchantTransactionIdentifier() {
        return merchantTransactionIdentifier;
    }

    public String getMerchantTransactionRequestType() {
        return merchantTransactionRequestType;
    }

    public String getResponseType() {
        return responseType;
    }

    public String getTransactionState() {
        return transactionState;
    }

    public String getMerchantAdditionalDetails() {
        return merchantAdditionalDetails;
    }

    public IngenicoPaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public String getError() {
        return error;
    }

    // Setter Methods

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public void setMerchantTransactionIdentifier(String merchantTransactionIdentifier) {
        this.merchantTransactionIdentifier = merchantTransactionIdentifier;
    }

    public void setMerchantTransactionRequestType(String merchantTransactionRequestType) {
        this.merchantTransactionRequestType = merchantTransactionRequestType;
    }

    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }

    public void setTransactionState(String transactionState) {
        this.transactionState = transactionState;
    }

    public void setMerchantAdditionalDetails(String merchantAdditionalDetails) {
        this.merchantAdditionalDetails = merchantAdditionalDetails;
    }

    public void setPaymentMethod(IngenicoPaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public void setError(String error) {
        this.error = error;
    }

    @Override
    public Map<String, String> getPersistentAttributes() {
        if (paymentMethod != null) {
            Map<String, String> attributes = new HashMap<>();
            attributes.put("token", paymentMethod.getToken());
            attributes.put("bankSelectionCode", paymentMethod.getBankSelectionCode());
            attributes.put("currency", String.valueOf("INR"));
            attributes.put("merchantTransactionIdentifier", getMerchantTransactionIdentifier());
            attributes.put("merchantCode", getMerchantCode());
            if (paymentMethod.getPaymentTransaction() != null) {
                attributes.put("dateTime", paymentMethod.getPaymentTransaction().getDateTime());
                attributes.put("refundIdentifier", paymentMethod.getPaymentTransaction().getRefundIdentifier());
                attributes.put("identifier", paymentMethod.getPaymentTransaction().getIdentifier());
            }
            return attributes;
        }
        return null;
    }

    @Override
    public String getOrderId() {
        return merchantTransactionIdentifier;
    }

    @Override
    public String getStatus() {
        if (paymentMethod != null && paymentMethod.getPaymentTransaction() != null) {
            return paymentMethod.getPaymentTransaction().getStatusMessage();
        }
        return null;
    }

    public String getStatusCode() {
        if (paymentMethod != null && paymentMethod.getPaymentTransaction() != null) {
            return paymentMethod.getPaymentTransaction().getStatusCode();
        }
        return "502";
    }

    @Override
    public String getReason() {
        return null;
    }

    @Override
    public String getTransactionId() {
        if(getPaymentMethod() != null && getPaymentMethod().getPaymentTransaction() != null) {
            return getPaymentMethod().getPaymentTransaction().getIdentifier();
        } else {
            return null;
        }
    }

	@Override
	public String getPartnerOrderId() {
		return merchantTransactionIdentifier;
	}
}
