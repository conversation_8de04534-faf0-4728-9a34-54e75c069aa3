package com.stpl.tech.master.payment.model.patymNew;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

public class PaytmRefundBody {

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date txnTimestamp;
    private String orderId;
    private String min;
    private String refId;
    private PaytmResponseResultInfo resultInfo;
    private String refundId;
    private BigDecimal refundAmount;

    public PaytmRefundBody() {
    }

    public Date getTxnTimestamp() {
        return txnTimestamp;
    }

    public void setTxnTimestamp(Date txnTimestamp) {
        this.txnTimestamp = txnTimestamp;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getMin() {
        return min;
    }

    public void setMin(String min) {
        this.min = min;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public PaytmResponseResultInfo getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(PaytmResponseResultInfo resultInfo) {
        this.resultInfo = resultInfo;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }
}
