
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "payment", "order", "invoice" })
public class Payload implements Serializable {
	private final static long serialVersionUID = -1380029622617784916L;

	@JsonProperty("payment")
	public Payment payment;
	@JsonProperty("order")
	public Order order;
	@JsonProperty("invoice")
	public Invoice invoice;

	public Payment getPayment() {
		return payment;
	}

	public void setPayment(Payment payment) {
		this.payment = payment;
	}

	public Order getOrder() {
		return order;
	}

	public void setOrder(Order order) {
		this.order = order;
	}

	public Invoice getInvoice() {
		return invoice;
	}

	public void setInvoice(Invoice invoice) {
		this.invoice = invoice;
	}

}
