package com.stpl.tech.master.payment.model.ingenico;

public class CustomerInfo {
    private String mobileNumber;
    private String emailID;
    private String identifier;
    private String accountNo;
    private String accountType;
    private String accountHolderName;
    private String aadharNo;


    // Getter Methods

    public String getMobileNumber() {
        return mobileNumber;
    }

    public String getEmailID() {
        return emailID;
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public String getAccountHolderName() {
        return accountHolderName;
    }

    public String getAadharNo() {
        return aadharNo;
    }

    // Setter Methods

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public void setEmailID(String emailID) {
        this.emailID = emailID;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public void setAccountHolderName(String accountHolderName) {
        this.accountHolderName = accountHolderName;
    }

    public void setAadharNo(String aadharNo) {
        this.aadharNo = aadharNo;
    }
}
