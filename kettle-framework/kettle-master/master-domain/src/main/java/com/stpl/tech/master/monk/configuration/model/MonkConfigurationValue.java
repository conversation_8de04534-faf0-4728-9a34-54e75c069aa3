//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.12 at 05:48:44 PM IST
//


package com.stpl.tech.master.monk.configuration.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for MonkConfigurationValue complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="MonkConfigurationValue"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="confId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="scope" type="{http://www.w3schools.com}MonkConfigurationScope"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}MonkConfigurationType"/&gt;
 *         &lt;element name="value" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MonkConfigurationValue", propOrder = {
    "confId",
    "scope",
    "type",
    "value"
})
public class MonkConfigurationValue {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer confId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected MonkConfigurationScope scope;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected MonkConfigurationType type;
    @XmlElement(required = true)
    protected String value;

    /**
     * Gets the value of the confId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getConfId() {
        return confId;
    }

    /**
     * Sets the value of the confId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setConfId(Integer value) {
        this.confId = value;
    }

    /**
     * Gets the value of the scope property.
     *
     * @return
     *     possible object is
     *     {@link MonkConfigurationScope }
     *
     */
    public MonkConfigurationScope getScope() {
        return scope;
    }

    /**
     * Sets the value of the scope property.
     *
     * @param value
     *     allowed object is
     *     {@link MonkConfigurationScope }
     *
     */
    public void setScope(MonkConfigurationScope value) {
        this.scope = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link MonkConfigurationType }
     *     
     */
    public MonkConfigurationType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link MonkConfigurationType }
     *     
     */
    public void setType(MonkConfigurationType value) {
        this.type = value;
    }

    /**
     * Gets the value of the value property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValue() {
        return value;
    }

    /**
     * Sets the value of the value property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValue(String value) {
        this.value = value;
    }

}
