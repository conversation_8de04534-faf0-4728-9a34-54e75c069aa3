package com.stpl.tech.master;

import java.util.Date;

public class OfferLastRedemptionView {
    private Integer customerId;
    private Date lastOrderTime;
    private Integer orderToday;
    private Integer orderInLastHour;

    public OfferLastRedemptionView() {
    }

    public OfferLastRedemptionView(Integer customerId, Date lastOrderTime, Integer orderToday, Integer orderInLastHour) {
        this.customerId = customerId;
        this.lastOrderTime = lastOrderTime;
        this.orderToday = orderToday;
        this.orderInLastHour = orderInLastHour;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    public Integer getOrderToday() {
        return orderToday;
    }

    public void setOrderToday(Integer orderToday) {
        this.orderToday = orderToday;
    }

    public Integer getOrderInLastHour() {
        return orderInLastHour;
    }

    public void setOrderInLastHour(Integer orderInLastHour) {
        this.orderInLastHour = orderInLastHour;
    }
}
