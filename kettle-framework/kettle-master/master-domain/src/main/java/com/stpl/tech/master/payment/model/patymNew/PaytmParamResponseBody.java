package com.stpl.tech.master.payment.model.patymNew;

public class PaytmParamResponseBody {

    private PaytmResponseResultInfo resultInfo;

    private String txnToken;

    private Boolean isPromoCodeValid;

    private Boolean authenticated;

    public PaytmParamResponseBody() {
    }

    public PaytmParamResponseBody(PaytmResponseResultInfo resultInfo, String txnToken,
                                  Boolean isPromoCodeValid, Boolean authenticated) {
        this.resultInfo = resultInfo;
        this.txnToken = txnToken;
        this.isPromoCodeValid = isPromoCodeValid;
        this.authenticated = authenticated;
    }

    public PaytmResponseResultInfo getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(PaytmResponseResultInfo resultInfo) {
        this.resultInfo = resultInfo;
    }

    public String getTxnToken() {
        return txnToken;
    }

    public void setTxnToken(String txnToken) {
        this.txnToken = txnToken;
    }

    public Boolean getPromoCodeValid() {
        return isPromoCodeValid;
    }

    public void setPromoCodeValid(Boolean promoCodeValid) {
        isPromoCodeValid = promoCodeValid;
    }

    public Boolean getAuthenticated() {
        return authenticated;
    }

    public void setAuthenticated(Boolean authenticated) {
        this.authenticated = authenticated;
    }
}
