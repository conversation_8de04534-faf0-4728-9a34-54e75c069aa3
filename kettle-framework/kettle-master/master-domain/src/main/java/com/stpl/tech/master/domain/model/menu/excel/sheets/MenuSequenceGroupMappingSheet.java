package com.stpl.tech.master.domain.model.menu.excel.sheets;

import com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil;

import java.util.Arrays;
import java.util.List;

public enum MenuSequenceGroupMappingSheet {

    GROUP_NAME("Cafe Group",Boolean.TRUE,Boolean.FALSE,String.class,null),
    MENU_SEQUENCE("MENU SEQUENCE",Boolean.TRUE,Boolean.FALSE,String.class,null),
    PARTNER_ID("PARTNER",Boolean.TRUE,Boolean.FALSE,Integer.class, MenuExcelUtil.getAcceptablePartnerIds()),
    BRAND_ID("BRAND ID",Boolean.TRUE,Boolean.FALSE,Integer.class,MenuExcelUtil.getAcceptableBrandIds()),
    MAPPING_STATUS("MAPPING STATUS",Boolean.TRUE,Boolean.FALSE,String.class,MenuExcelUtil.getAcceptableStatusValues()),
    UPDATE("UPDATE",Boolean.TRUE,Boolean.FALSE,String.class,MenuExcelUtil.getAcceptableUpdateValues()),
    SLOTS("SLOTS",Boolean.TRUE,Boolean.FALSE,String.class,MenuExcelUtil.getAllDaySlots()),
    IN_VALID_COLUMN("",null,null,null,null);

    MenuSequenceGroupMappingSheet(String columnName , Boolean isMandatory ,Boolean nullable
            , Class<?> dataType , List<?> acceptedValues){
        this.columnName = columnName;
        this.isMandatory = isMandatory;
        this.nullable = nullable;
        this.dataType = dataType;
        this.acceptableValues = acceptedValues;
    }

    private String columnName;

    private Boolean isMandatory;

    private Boolean nullable;

    private Class<?> dataType;

    private List<?> acceptableValues;

    public  String getColumnName(){return this.columnName;}

    public Boolean isMandatory(){return this.isMandatory;}

    public Boolean nullable(){return  this.nullable;}

    public Class<?> getDataType(){return this.dataType;}

    public List<?> getAcceptableValues(){return acceptableValues;}

    public static MenuSequenceGroupMappingSheet getColumnEnum(String columnName){
        return Arrays.stream(MenuSequenceGroupMappingSheet.values()).filter(column -> column.columnName.equalsIgnoreCase(columnName))
                .findFirst().orElse(MenuSequenceGroupMappingSheet.IN_VALID_COLUMN);
    }
}
