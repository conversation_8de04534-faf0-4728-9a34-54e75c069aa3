
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "entity" })
public class Payment implements Serializable {
	private final static long serialVersionUID = 8380067245878790338L;
	@JsonProperty("entity")
	public PaymentEntity entity;

	public PaymentEntity getEntity() {
		return entity;
	}

	public void setEntity(PaymentEntity entity) {
		this.entity = entity;
	}

}
