package com.stpl.tech.master.locality.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Unit Budget Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class LocalityWrapper {

	@ExcelField
	protected String locality;
	@ExcelField
	protected String city;
	@ExcelField
	protected String state;
	@ExcelField
	protected String country;
	@ExcelField
	protected boolean defaultLocality;
	@ExcelField
	protected String primaryUnitName;
	@ExcelField
	protected int primaryUnitId;
	@ExcelField
	protected boolean skipPrimaryDeliveryCharge;
	@ExcelField
	protected boolean skipPrimaryPackagingCharge;
	@ExcelField
	protected String secondaryUnitName;
	@ExcelField
	protected int secondaryUnitId;
	@ExcelField
	protected boolean skipSecondaryDeliveryCharge;
	@ExcelField
	protected boolean skipSecondaryPackagingCharge;

	public String getLocality() {
		return locality;
	}

	public void setLocality(String locality) {
		this.locality = locality;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public boolean isDefaultLocality() {
		return defaultLocality;
	}

	public void setDefaultLocality(boolean defaultLocality) {
		this.defaultLocality = defaultLocality;
	}

	public String getPrimaryUnitName() {
		return primaryUnitName;
	}

	public void setPrimaryUnitName(String primaryUnitName) {
		this.primaryUnitName = primaryUnitName;
	}

	public int getPrimaryUnitId() {
		return primaryUnitId;
	}

	public void setPrimaryUnitId(int primaryUnitId) {
		this.primaryUnitId = primaryUnitId;
	}

	public boolean isSkipPrimaryDeliveryCharge() {
		return skipPrimaryDeliveryCharge;
	}

	public void setSkipPrimaryDeliveryCharge(boolean skipPrimaryDeliveryCharge) {
		this.skipPrimaryDeliveryCharge = skipPrimaryDeliveryCharge;
	}

	public boolean isSkipPrimaryPackagingCharge() {
		return skipPrimaryPackagingCharge;
	}

	public void setSkipPrimaryPackagingCharge(boolean skipPrimaryPackagingCharge) {
		this.skipPrimaryPackagingCharge = skipPrimaryPackagingCharge;
	}

	public String getSecondaryUnitName() {
		return secondaryUnitName;
	}

	public void setSecondaryUnitName(String secondaryUnitName) {
		this.secondaryUnitName = secondaryUnitName;
	}

	public int getSecondaryUnitId() {
		return secondaryUnitId;
	}

	public void setSecondaryUnitId(int secondaryUnitId) {
		this.secondaryUnitId = secondaryUnitId;
	}

	public boolean isSkipSecondaryDeliveryCharge() {
		return skipSecondaryDeliveryCharge;
	}

	public void setSkipSecondaryDeliveryCharge(boolean skipSecondaryDeliveryCharge) {
		this.skipSecondaryDeliveryCharge = skipSecondaryDeliveryCharge;
	}

	public boolean isSkipSecondaryPackagingCharge() {
		return skipSecondaryPackagingCharge;
	}

	public void setSkipSecondaryPackagingCharge(boolean skipSecondaryPackagingCharge) {
		this.skipSecondaryPackagingCharge = skipSecondaryPackagingCharge;
	}

}
