
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "id", "entity", "amount", "currency", "receipt", "status", "attempts", "notes", "created_at" })
public class OrderEntity implements Serializable {

	private final static long serialVersionUID = -8836699335043010568L;
	@JsonProperty("id")
	public String id;
	@JsonProperty("entity")
	public String entity;
	@JsonProperty("amount")
	public Integer amount;
	@JsonProperty("amount_paid")
	public Integer paidAmount;
	@JsonProperty("amount_due")
	public Integer dueAmount;
	@JsonProperty("currency")
	public String currency;
	@JsonProperty("receipt")
	public String receipt;
	@JsonProperty("offer_id")
	public String offerId;
	@JsonProperty("status")
	public String status;
	@JsonProperty("attempts")
	public Integer attempts;
	@JsonProperty("notes")
	public Object notes;
	@JsonProperty("created_at")
	public long createdAt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getEntity() {
		return entity;
	}

	public void setEntity(String entity) {
		this.entity = entity;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getReceipt() {
		return receipt;
	}

	public void setReceipt(String receipt) {
		this.receipt = receipt;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getAttempts() {
		return attempts;
	}

	public void setAttempts(Integer attempts) {
		this.attempts = attempts;
	}

	public Object getNotes() {
		return notes;
	}

	public void setNotes(Object notes) {
		this.notes = notes;
	}

	public long getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(long createdAt) {
		this.createdAt = createdAt;
	}

	public Integer getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(Integer paidAmount) {
		this.paidAmount = paidAmount;
	}

	public Integer getDueAmount() {
		return dueAmount;
	}

	public void setDueAmount(Integer dueAmount) {
		this.dueAmount = dueAmount;
	}

	public String getOfferId() {
		return offerId;
	}

	public void setOfferId(String offerId) {
		this.offerId = offerId;
	}

	
}
