package com.stpl.tech.master.payment.model.ingenico;

import com.stpl.tech.util.domain.adapter.BigDecimalAdapter;

import java.math.BigDecimal;

public class IngenicoPaymentTransaction {
    private String amount;
    private String balanceAmount;
    private String bankReferenceIdentifier;
    private String dateTime;
    private String errorMessage;
    private String identifier;
    private String refundIdentifier;
    private String statusCode;
    private String statusMessage;
    private String instruction = null;
    private String reference;

    // Getter Methods

    public String getAmount() {
        return amount;
    }

    public String getBalanceAmount() {
        return balanceAmount;
    }

    public String getBankReferenceIdentifier() {
        return bankReferenceIdentifier;
    }

    public String getDateTime() {
        return dateTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getRefundIdentifier() {
        return refundIdentifier;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public String getInstruction() {
        return instruction;
    }

    public String getReference() {
        return reference;
    }


    // Setter Methods

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setBalanceAmount(String balanceAmount) {
        this.balanceAmount = balanceAmount;
    }

    public void setBankReferenceIdentifier(String bankReferenceIdentifier) {
        this.bankReferenceIdentifier = bankReferenceIdentifier;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public void setRefundIdentifier(String refundIdentifier) {
        this.refundIdentifier = refundIdentifier;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

}
