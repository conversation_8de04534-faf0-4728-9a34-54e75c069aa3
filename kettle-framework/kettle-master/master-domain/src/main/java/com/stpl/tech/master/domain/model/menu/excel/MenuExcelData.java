package com.stpl.tech.master.domain.model.menu.excel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MenuExcelData {
    private Map<String, Object> sheets = new HashMap<>();

    public void setField(String key, Object value) {
        sheets.put(key, value);
    }

    public Object getField(String key) {
        return sheets.get(key);
    }

    public List<String> getKeys(){ return new ArrayList<>(sheets.keySet());}

    @Override
    public String toString() {
        return "MenuExcelData{" +
                "sheets=" + sheets +
                '}';
    }
}

