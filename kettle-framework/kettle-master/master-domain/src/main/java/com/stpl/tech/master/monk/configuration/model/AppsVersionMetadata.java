package com.stpl.tech.master.monk.configuration.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.stpl.tech.master.domain.model.SwitchStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-05-2018.
 */

@Document(collection = "appVersionMetaData")
public class AppsVersionMetadata {

    @Id
    private String id;
    private String major;
    private String minor;
    private String patch;
    private int version;
    private String name;
    private SwitchStatus status;
    private List<AppBuildData> apps;
    private Date uploadDate;

    public AppsVersionMetadata() {}

    public AppsVersionMetadata(Integer major, Integer minor, Integer patch, int version) {
        this.major = major.toString();
        this.minor = minor.toString();
        this.patch = patch.toString();
        this.version = version;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getMinor() {
        return minor;
    }

    public void setMinor(String minor) {
        this.minor = minor;
    }

    public String getPatch() {
        return patch;
    }

    public void setPatch(String patch) {
        this.patch = patch;
    }

    public SwitchStatus getStatus() {
        return status;
    }

    public void setStatus(SwitchStatus status) {
        this.status = status;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public List<AppBuildData> getApps() {
        if (apps == null){
            apps = new ArrayList<>();
        }
        return apps;
    }

    public Date getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
