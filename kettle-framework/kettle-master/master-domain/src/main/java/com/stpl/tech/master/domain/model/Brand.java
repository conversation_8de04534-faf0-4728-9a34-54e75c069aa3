/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.04.04 at 12:22:00 PM IST
//

package com.stpl.tech.master.domain.model;

import com.stpl.tech.kettle.report.metadata.model.comparator.HasId;
import com.stpl.tech.kettle.report.metadata.model.comparator.HasStatus;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {"brandId", "brandName", "brandCode"})
@XmlRootElement(name = "Brand")
public class Brand implements Serializable, Comparable<Brand>, HasId, HasStatus {

    private static final long serialVersionUID = -93045179307038127L;
    private Integer brandId;
    private String brandName;
    private String brandCode;
    private String tagLine;
    private String domain;
    private String billTag;
    private String websiteLink;
    private String status;
    private String supportContact;
    private String supportEmail;
    private String verbiage;
    private Boolean sendEmail;
    private Boolean sendWelcomeMessage;
    private Boolean awardLoyalty;
    private Boolean sendNPS;

    private String feedBackUrl;
    private String feedBackEndpointNPSDeliveryOnlyOrder;
    private Boolean sendFeedbackMessageDeliverySwiggy;
    private String feedbackEndpointDinein;
    private String feedbackEndpointDelivery;
    private String feedbackEndpointLowRatingDinein;
    private String feedbackEndpointLowRatingDelivery;
    private String feedbackEndpointRedirectUrl;
    private String feedbackEndpointNPSCafe;
    private String feedbackEndpointNPSDelivery;

    private String verificationEmailTemplate;

    private String smsId;
    private String  internalOrderFeedbackUrl;
    private String  chaayosSubscription;
    private String googleAdsCustomerId;
    //monk metaData
    private String monkImageLogoUrl;
    private String monkWelcomeVideoUrl;
    private String monkScreenSaver;
    private String monkBackgroundUrl;
    private String deliveryOrderCancellationTime;
    private String dineInOrderCancellationTime;
    private String brandContactCode;


    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getTagLine() {
        return tagLine;
    }

    public void setTagLine(String tagLine) {
        this.tagLine = tagLine;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getBillTag() {
        return billTag;
    }

    public void setBillTag(String billTag) {
        this.billTag = billTag;
    }

    public String getWebsiteLink() {
        return websiteLink;
    }

    public void setWebsiteLink(String websiteLink) {
        this.websiteLink = websiteLink;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSupportContact() {
        return supportContact;
    }

    public void setSupportContact(String supportContact) {
        this.supportContact = supportContact;
    }

    public String getSupportEmail() {
        return supportEmail;
    }

    public void setSupportEmail(String supportEmail) {
        this.supportEmail = supportEmail;
    }

    public String getVerbiage() {
        return verbiage;
    }

    public void setVerbiage(String verbiage) {
        this.verbiage = verbiage;
    }

    public Boolean getSendEmail() {
        return sendEmail;
    }

    public void setSendEmail(Boolean sendEmail) {
        this.sendEmail = sendEmail;
    }

    public Boolean getSendWelcomeMessage() {
        return sendWelcomeMessage;
    }

    public void setSendWelcomeMessage(Boolean sendWelcomeMessage) {
        this.sendWelcomeMessage = sendWelcomeMessage;
    }

    public Boolean getAwardLoyalty() {
        return awardLoyalty;
    }

    public void setAwardLoyalty(Boolean awardLoyalty) {
        this.awardLoyalty = awardLoyalty;
    }

    @Override
    public Object objectId() {
        return this.brandId;
    }

    @Override
    public String currentStatus() {
        return this.status;
    }

    @Override
    public int compareTo(Brand o) {
        return o.brandId.compareTo(brandId);
    }


    public Boolean getSendNPS() {
        return sendNPS;
    }

    public void setSendNPS(Boolean sendNPS) {
        this.sendNPS = sendNPS;
    }

    public String getFeedBackUrl() {
        return feedBackUrl;
    }

    public void setFeedBackUrl(String feedBackUrl) {
        this.feedBackUrl = feedBackUrl;
    }

    public String getFeedBackEndpointNPSDeliveryOnlyOrder() {
        return feedBackEndpointNPSDeliveryOnlyOrder;
    }

    public void setFeedBackEndpointNPSDeliveryOnlyOrder(String feedBackEndpointNPSDeliveryOnlyOrder) {
        this.feedBackEndpointNPSDeliveryOnlyOrder = feedBackEndpointNPSDeliveryOnlyOrder;
    }

    public Boolean getSendFeedbackMessageDeliverySwiggy() {
        return sendFeedbackMessageDeliverySwiggy;
    }

    public void setSendFeedbackMessageDeliverySwiggy(Boolean sendFeedbackMessageDeliverySwiggy) {
        this.sendFeedbackMessageDeliverySwiggy = sendFeedbackMessageDeliverySwiggy;
    }

    public String getFeedbackEndpointDinein() {
        return feedbackEndpointDinein;
    }

    public void setFeedbackEndpointDinein(String feedbackEndpointDinein) {
        this.feedbackEndpointDinein = feedbackEndpointDinein;
    }

    public String getFeedbackEndpointDelivery() {
        return feedbackEndpointDelivery;
    }

    public void setFeedbackEndpointDelivery(String feedbackEndpointDelivery) {
        this.feedbackEndpointDelivery = feedbackEndpointDelivery;
    }

    public String getFeedbackEndpointLowRatingDinein() {
        return feedbackEndpointLowRatingDinein;
    }

    public void setFeedbackEndpointLowRatingDinein(String feedbackEndpointLowRatingDinein) {
        this.feedbackEndpointLowRatingDinein = feedbackEndpointLowRatingDinein;
    }

    public String getFeedbackEndpointLowRatingDelivery() {
        return feedbackEndpointLowRatingDelivery;
    }

    public void setFeedbackEndpointLowRatingDelivery(String feedbackEndpointLowRatingDelivery) {
        this.feedbackEndpointLowRatingDelivery = feedbackEndpointLowRatingDelivery;
    }

    public String getFeedbackEndpointRedirectUrl() {
        return feedbackEndpointRedirectUrl;
    }

    public void setFeedbackEndpointRedirectUrl(String feedbackEndpointRedirectUrl) {
        this.feedbackEndpointRedirectUrl = feedbackEndpointRedirectUrl;
    }

    public String getFeedbackEndpointNPSCafe() {
        return feedbackEndpointNPSCafe;
    }

    public void setFeedbackEndpointNPSCafe(String feedbackEndpointNPSCafe) {
        this.feedbackEndpointNPSCafe = feedbackEndpointNPSCafe;
    }

    public String getFeedbackEndpointNPSDelivery() {
        return feedbackEndpointNPSDelivery;
    }

    public void setFeedbackEndpointNPSDelivery(String feedbackEndpointNPSDelivery) {
        this.feedbackEndpointNPSDelivery = feedbackEndpointNPSDelivery;
    }

    public String getVerificationEmailTemplate() {
        return verificationEmailTemplate;
    }

    public void setVerificationEmailTemplate(String verificationEmailTemplate) {
        this.verificationEmailTemplate = verificationEmailTemplate;
    }

    public String getSmsId() {
        return smsId;
    }

    public void setSmsId(String smsId) {
        this.smsId = smsId;
    }

    public String getMonkImageLogoUrl() {
        return monkImageLogoUrl;
    }

    public void setMonkImageLogoUrl(String monkImageLogoUrl) {
        this.monkImageLogoUrl = monkImageLogoUrl;
    }

    public String getMonkWelcomeVideoUrl() {
        return monkWelcomeVideoUrl;
    }

    public void setMonkWelcomeVideoUrl(String monkWelcomeVideoUrl) {
        this.monkWelcomeVideoUrl = monkWelcomeVideoUrl;
    }

    public String getMonkScreenSaver() {
        return monkScreenSaver;
    }

    public void setMonkScreenSaver(String monkScreenSaver) {
        this.monkScreenSaver = monkScreenSaver;
    }

    public String getMonkBackgroundUrl() {
        return monkBackgroundUrl;
    }

    public void setMonkBackgroundUrl(String monkBackgroundUrl) {
        this.monkBackgroundUrl = monkBackgroundUrl;
    }

    public String getInternalOrderFeedbackUrl() {
        return internalOrderFeedbackUrl;
    }

    public void setInternalOrderFeedbackUrl(String internalOrderFeedbackUrl) {
        this.internalOrderFeedbackUrl = internalOrderFeedbackUrl;
    }

    public String getChaayosSubscription() {
        return chaayosSubscription;
    }

    public void setChaayosSubscription(String chaayosSubscription) {
        this.chaayosSubscription = chaayosSubscription;
    }

    public String getGoogleAdsCustomerId() {
        return googleAdsCustomerId;
    }

    public void setGoogleAdsCustomerId(String googleAdsCustomerId) {
        this.googleAdsCustomerId = googleAdsCustomerId;
    }

    public String getDeliveryOrderCancellationTime() {
        return deliveryOrderCancellationTime;
    }

    public void setDeliveryOrderCancellationTime(String deliveryOrderCancellationTime) {
        this.deliveryOrderCancellationTime = deliveryOrderCancellationTime;
    }

    public String getDineInOrderCancellationTime() {
        return dineInOrderCancellationTime;
    }

    public void setDineInOrderCancellationTime(String dineInOrderCancellationTime) {
        this.dineInOrderCancellationTime = dineInOrderCancellationTime;
    }

    public String getBrandContactCode() {
        return brandContactCode;
    }

    public void setBrandContactCode(String brandContactCode) {
        this.brandContactCode = brandContactCode;
    }
}
