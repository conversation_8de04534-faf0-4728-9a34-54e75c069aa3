package com.stpl.tech.master.payment.model.gpay;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequest;

public class GPayPaymentRequest implements Serializable, PaymentRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3001643661907595810L;
	private GPayPaymentPayload payload;
	private OrderPaymentRequest order;
	private String status;

	public GPayPaymentPayload getPayload() {
		return payload;
	}

	public void setPayload(GPayPaymentPayload payload) {
		this.payload = payload;
	}

	public OrderPaymentRequest getOrder() {
		return order;
	}

	public void setOrder(OrderPaymentRequest order) {
		this.order = order;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Override
	public String getPartnerOrderId() {
		return payload.getTransactionDetails().getTransactionId();
	}

	@Override
	public Map<String, String> getPersistentAttributes() {
		Map<String, String> attributes = new HashMap<>();
		attributes.put("transactionId", payload.getTransactionDetails().getTransactionId());
		attributes.put("amount", String.valueOf(payload.getTransactionDetails().getAmount().getUnits()));
		attributes.put("qrType", payload.getQrType().name());
		attributes.put("merchantId", payload.getMerchantInfo().getGoogleMerchantId());
		return attributes;
	}

	@Override
	public String getStatus() {
		return status;
	}

}
