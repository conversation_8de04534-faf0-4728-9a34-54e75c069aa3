/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.04 at 12:22:00 PM IST 
//

package com.stpl.tech.master.readonly.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.recipe.read.model.RecipeDetailVO;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductPrice", propOrder = {"dimension", "price", "recipe", "recipeId", "customize", "dimensionDescriptor", "aliasProductName"})
public class ProductPriceVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -5973360712972289240L;
    @XmlElement(required = true)
    protected String dimension;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal price;
    @XmlElement(required = true)
    protected RecipeDetailVO recipe;
    @XmlElement(required = true)
    protected int recipeId;
    @XmlElement(required = true)
    protected Boolean customize;
    @XmlElement(required = true)
    protected String dimensionDescriptor;
    @XmlElement(required = true)
    protected String aliasProductName;
    protected String profile;
    protected BigDecimal originalPrice;
    @XmlElement(required = true)
    protected Boolean isDeliveryOnlyProduct;
    @XmlElement(required = true)
    protected Boolean pickDineInConsumables;

    public ProductPriceVO() {

    }

    public ProductPriceVO(ProductPrice p) {
        this.dimension = p.getDimension();
        this.price = p.getPrice();
        if (p.isCustomize() != null && p.isCustomize()) {
            this.recipe = new RecipeDetailVO(p.getRecipe());
        }
        if (p.getRecipe() != null) {
            this.recipeId = p.getRecipe().getRecipeId();
        }
        this.customize = p.isCustomize();
        this.dimensionDescriptor = p.getDimensionDescriptor();
        this.aliasProductName = p.getAliasProductName();
        this.profile = p.getProfile();
        this.isDeliveryOnlyProduct= p.getIsDeliveryOnlyProduct();
        this.pickDineInConsumables = p.getPickDineInConsumables();
    }

    public ProductPriceVO(ProductPriceVO p) {
        this.dimension = p.getDimension();
        this.price = p.getPrice();
        if (p.getRecipe() != null) {
            this.recipeId = p.getRecipe().getRecipeId();
        }
        this.customize = p.getCustomize();
        this.dimensionDescriptor = p.getDimensionDescriptor();
        this.aliasProductName = p.getAliasProductName();
        this.profile = p.getProfile();
    }

    /**
     * Gets the value of the dimension property.
     *
     * @return possible object is {@link String }
     */
    public String getDimension() {
        return dimension;
    }

    /**
     * Sets the value of the dimension property.
     *
     * @param value allowed object is {@link String }
     */
    public void setDimension(String value) {
        this.dimension = value;
    }

    /**
     * Gets the value of the price property.
     *
     * @return possible object is {@link String }
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * Sets the value of the price property.
     *
     * @param value allowed object is {@link String }
     */
    public void setPrice(BigDecimal value) {
        this.price = value;
    }

    public void setCustomize(Boolean customize) {
        this.customize = customize;
    }

    public RecipeDetailVO getRecipe() {
        return recipe;
    }

    public void setRecipe(RecipeDetailVO recipe) {
        this.recipe = recipe;
    }

    public Boolean getCustomize() {
        return customize;
    }

    public int getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(int recipeId) {
        this.recipeId = recipeId;
    }

    public String getDimensionDescriptor() {
        return dimensionDescriptor;
    }

    public void setDimensionDescriptor(String dimensionDescriptor) {
        this.dimensionDescriptor = dimensionDescriptor;
    }

    public String getAliasProductName() {
        return aliasProductName;
    }

    public void setAliasProductName(String aliasProductName) {
        this.aliasProductName = aliasProductName;
    }

    public Boolean getIsDeliveryOnlyProduct() {
        return isDeliveryOnlyProduct;
    }

    public void setIsDeliveryOnlyProduct(Boolean deliveryOnlyProduct) {
        this.isDeliveryOnlyProduct = deliveryOnlyProduct;
    }

    public Boolean getPickDineInConsumables() {
        return pickDineInConsumables;
    }

    public void setPickDineInConsumables(Boolean pickDineInConsumables) {
        this.pickDineInConsumables = pickDineInConsumables;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }
}
