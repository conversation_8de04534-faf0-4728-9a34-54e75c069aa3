
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "entity" })
public class Invoice implements Serializable {
	private final static long serialVersionUID = 8012511408200060265L;

	@JsonProperty("entity")
	public InvoiceEntity entity;

	public InvoiceEntity getEntity() {
		return entity;
	}

	public void setEntity(InvoiceEntity entity) {
		this.entity = entity;
	}

}
