package com.stpl.tech.master.domain.model.menu.excel.sheets;

import com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil.getAcceptableBrandIds;
import static com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil.getAcceptablePartnerIds;
import static com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil.getAcceptableStatusValues;
import static com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil.getAcceptableUpdateValues;

public enum RecommendationMappingSheet {

    GROUP_NAME("Recommendation Group",Boolean.TRUE,Boolean.FALSE,String.class,null),
    PRODUCT_IDS("Product IDs",Boolean.TRUE,Boolean.FALSE,String.class,null),
    DAY_SLOT("DAY_SLOT",Boolean.TRUE,Boolean.FALSE,null, MenuExcelUtil.getAllDaySlots()),
    BRAND_ID("BRAND_ID",Boolean.TRUE,Boolean.FALSE,Integer.class,getAcceptableBrandIds()),
    PARTNER_ID("PARTNER_ID",Boolean.TRUE,Boolean.FALSE,Integer.class,getAcceptablePartnerIds()),
    UPDATE("UPDATE",Boolean.TRUE,Boolean.FALSE,String.class,getAcceptableUpdateValues()),
    STATUS("STATUS",Boolean.TRUE,Boolean.FALSE,String.class,getAcceptableStatusValues()),
    IN_VALID_COLUMN("",null,null,null,null);
    RecommendationMappingSheet(String columnName , Boolean isMandatory , Boolean nullable
            , Class<?> dataType , List<?> acceptedValues){
        this.columnName = columnName;
        this.isMandatory = isMandatory;
        this.nullable = nullable;
        this.dataType = dataType;
        this.acceptableValues = acceptedValues;
    }
    private String columnName;

    private Boolean isMandatory;

    private Boolean nullable;

    private Class<?> dataType;

    private List<?> acceptableValues;

    public  String getColumnName(){return this.columnName;}

    public Boolean isMandatory(){return this.isMandatory;}

    public Boolean nullable(){return  this.nullable;}

    public Class<?> getDataType(){return this.dataType;}

    public List<?> getAcceptableValues(){return acceptableValues;}

    public static RecommendationMappingSheet getColumnEnum(String columnName){
        return Arrays.stream(RecommendationMappingSheet.values()).filter(column -> column.columnName.equalsIgnoreCase(columnName))
                .findFirst().orElse(RecommendationMappingSheet.IN_VALID_COLUMN);
    }
}
