package com.stpl.tech.master.payment.model.ezetap;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;

public class EzetapServiceRequest implements Serializable{

	/**
	 *
	 */
	private static final long serialVersionUID = 1131273196808454688L;
	@JsonProperty("receipt")
	private String orderId;
	@JsonProperty("currency")
	private String currency;
	@JsonProperty("amount")
	private int transactionAmount;
	@JsonProperty("payment_capture")
	private boolean paymentCapture;
	@JsonIgnore
	private String status;

	public EzetapServiceRequest() {

	}

	public EzetapServiceRequest(String generatedOrderId, BigDecimal paidAmount) {
		this.orderId = generatedOrderId;
		this.transactionAmount = paidAmount.intValue();
		this.currency = "INR";
		this.paymentCapture = true;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public int getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(int transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public boolean getPaymentCapture() {
		return paymentCapture;
	}

	public void setPaymentCapture(boolean paymentCapture) {
		this.paymentCapture = paymentCapture;
	}

}
