//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.01.20 at 04:03:43 PM IST
//


package com.stpl.tech.master.locality.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.Adapter2;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;


/**
 * <p>Java class for LocalityMapping complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="LocalityMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="objectId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="primaryCOD" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="secondaryCOD" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="tertiaryCOD" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="locality" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="createdAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="primaryUnitDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="primaryUnitId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="secUnitDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="secondaryUnitId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="tertiaryUnitDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="tertiaryUnitId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="updatedAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "LocalityMapping", propOrder = {
    "objectId",
    "primaryCOD",
    "secondaryCOD",
    "city",
    "state",
    "country",
    "locality",
        "zone",
    "createdAt",
    "primaryUnitId",
    "secondaryUnitId",
    "tertiaryUnitId",
    "updatedAt"
})
@Document(collection = "LocalityMappings")
public class  LocalityMapping implements Serializable{

    private static final long serialVersionUID = 3420989246144929161L;

    @Id
    @XmlElement(required = true)
    protected String objectId;
    @XmlElement(required = true)
    protected String primaryCOD;
    @XmlElement(required = true)
    protected String secondaryCOD;
    @XmlElement(required = true)
    protected String city;
    @XmlElement(required = true)
    protected String state;
    @XmlElement(required = true, defaultValue = "India")
    protected String country;
    @XmlElement(required = true)
    protected String locality;
    @XmlElement(required = true)
    protected String zone;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    protected Date createdAt;

    @XmlElement(required = true)
    protected String primaryUnitId;

    @XmlElement(required = true)
    protected String secondaryUnitId;

    @XmlElement(required = true)
    protected String tertiaryUnitId;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    protected Date updatedAt;

    /**
     * Gets the value of the objectId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getObjectId() {
        return objectId;
    }

    /**
     * Sets the value of the objectId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setObjectId(String value) {
        this.objectId = value;
    }

    /**
     * Gets the value of the primaryCOD property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getPrimaryCOD() {
        return primaryCOD;
    }

    /**
     * Sets the value of the primaryCOD property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setPrimaryCOD(String value) {
        this.primaryCOD = value;
    }

    /**
     * Gets the value of the secondaryCOD property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSecondaryCOD() {
        return secondaryCOD;
    }

    /**
     * Sets the value of the secondaryCOD property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSecondaryCOD(String value) {
        this.secondaryCOD = value;
    }

    /**
     * Gets the value of the city property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the state property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getState() {
        return state;
    }

    /**
     * Sets the value of the state property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setState(String value) {
        this.state = value;
    }

    /**
     * Gets the value of the country property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCountry() {
        return country;
    }

    /**
     * Sets the value of the country property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCountry(String value) {
        this.country = value;
    }

    /**
     * Gets the value of the locality property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getLocality() {
        return locality;
    }

    /**
     * Sets the value of the locality property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLocality(String value) {
        this.locality = value;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    /**
     * Gets the value of the createdAt property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * Sets the value of the createdAt property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCreatedAt(Date value) {
        this.createdAt = value;
    }

    /**
     * Gets the value of the primaryUnitId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getPrimaryUnitId() {
        return primaryUnitId;
    }

    /**
     * Sets the value of the primaryUnitId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setPrimaryUnitId(String value) {
        this.primaryUnitId = value;
    }

    /**
     * Gets the value of the secondaryUnitId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSecondaryUnitId() {
        return secondaryUnitId;
    }

    /**
     * Sets the value of the secondaryUnitId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSecondaryUnitId(String value) {
        this.secondaryUnitId = value;
    }

    /**
     * Gets the value of the tertiaryUnitId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTertiaryUnitId() {
        return tertiaryUnitId;
    }

    /**
     * Sets the value of the tertiaryUnitId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTertiaryUnitId(String value) {
        this.tertiaryUnitId = value;
    }

    /**
     * Gets the value of the updatedAt property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * Sets the value of the updatedAt property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUpdatedAt(Date value) {
        this.updatedAt = value;
    }

}
