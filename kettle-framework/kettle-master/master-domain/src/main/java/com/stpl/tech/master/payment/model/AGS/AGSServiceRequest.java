package com.stpl.tech.master.payment.model.AGS;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;

public class AGSServiceRequest implements Serializable {

    private static final long serialVersionUID = -1343236611395932396L;

    @JsonProperty("receipt")
    private String orderId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("amount")
    private int transactionAmount;
    @JsonProperty("payment_capture")
    private boolean paymentCapture;
    @JsonIgnore
    private String status;

    public AGSServiceRequest() {

    }

    public AGSServiceRequest(String generatedOrderId, BigDecimal paidAmount) {
        this.orderId = generatedOrderId;
        this.transactionAmount = paidAmount.intValue();
        this.currency = "INR";
        this.paymentCapture = true;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public int getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(int transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean getPaymentCapture() {
        return paymentCapture;
    }

    public void setPaymentCapture(boolean paymentCapture) {
        this.paymentCapture = paymentCapture;
    }

}