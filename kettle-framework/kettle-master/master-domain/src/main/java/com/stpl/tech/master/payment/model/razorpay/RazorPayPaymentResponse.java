package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentResponse;

public class RazorPayPaymentResponse implements Serializable, PaymentResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8606066843308061274L;

	@JsonProperty("razorpay_payment_id")
	private String transactionId;
	@JsonProperty("externalOrderId")
	private String orderId;
	@JsonProperty("razorpay_signature")
	private String signature;
	@JsonProperty("razorpay_order_id")
	private String razorPayOrderId;
	@JsonProperty("status")
	private String status;

	@Override
	public Map<String, String> getPersistentAttributes() {
		HashMap<String, String> parameters = new HashMap<String, String>();
		parameters.put("razorpay_payment_id", transactionId);
		parameters.put("razorpay_order_id", orderId);
		parameters.put("razorpay_payment_id", transactionId);
		parameters.put("status", status);
		return parameters;

	}

	@Override
	public String getOrderId() {
		return orderId;
	}

	@Override
	public String getStatus() {
		return status;
	}

	@Override
	public String getTransactionId() {
		return transactionId;
	}

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getRazorPayOrderId() {
		return razorPayOrderId;
	}

	public void setRazorPayOrderId(String razorPayOrderId) {
		this.razorPayOrderId = razorPayOrderId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Override
	public String getReason() {
		return "Failed to process in external system";
	}

	@Override
	public String getPartnerOrderId() {
		return razorPayOrderId;
	}

}
