package com.stpl.tech.master.payment.model.ingenico;

import java.math.BigDecimal;

public class IngenicoTransaction {
    private String deviceIdentifier;
    private String smsSending;
    private BigDecimal amount;
    private String forced3DSCall;
    private String type;
    private String description;
    private String currency;
    private String isRegistration;
    private String identifier;
    private String dateTime;
    private String token;
    private String securityToken;
    private String subType;
    private String requestType;
    private String reference;
    private String merchantInitiated;
    private String tenureId;


    // Getter Methods

    public String getDeviceIdentifier() {
        return deviceIdentifier;
    }

    public String getSmsSending() {
        return smsSending;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public String getForced3DSCall() {
        return forced3DSCall;
    }

    public String getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public String getCurrency() {
        return currency;
    }

    public String getIsRegistration() {
        return isRegistration;
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getDateTime() {
        return dateTime;
    }

    public String getToken() {
        return token;
    }

    public String getSecurityToken() {
        return securityToken;
    }

    public String getSubType() {
        return subType;
    }

    public String getRequestType() {
        return requestType;
    }

    public String getReference() {
        return reference;
    }

    public String getMerchantInitiated() {
        return merchantInitiated;
    }

    public String getTenureId() {
        return tenureId;
    }

    // Setter Methods

    public void setDeviceIdentifier(String deviceIdentifier) {
        this.deviceIdentifier = deviceIdentifier;
    }

    public void setSmsSending(String smsSending) {
        this.smsSending = smsSending;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setForced3DSCall(String forced3DSCall) {
        this.forced3DSCall = forced3DSCall;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public void setIsRegistration(String isRegistration) {
        this.isRegistration = isRegistration;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public void setSecurityToken(String securityToken) {
        this.securityToken = securityToken;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public void setMerchantInitiated(String merchantInitiated) {
        this.merchantInitiated = merchantInitiated;
    }

    public void setTenureId(String tenureId) {
        this.tenureId = tenureId;
    }
}
