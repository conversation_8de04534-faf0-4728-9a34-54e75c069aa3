package com.stpl.tech.master.payment.model.paytmUpi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentResponse;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class PaytmUpiTransactionResponse{
    @JsonProperty("MID")
    private String mid;
    @JsonProperty("TXNID")
    private String txnId;
    @JsonProperty("ORDERID")
    private String orderId;
    @JsonProperty("BANKTXNID")
    private String bankTxnId;
    @JsonProperty("TXNAMOUNT")
    private BigDecimal txnAmount;
    @JsonProperty("TXNTYPE")
    private String txnType;
    @JsonProperty("STATUS")
    private String status;
    @JsonProperty("RESPCODE")
    private String respCode;
    @JsonProperty("RESPMSG")
    private String respMsg;
    @JsonProperty("TXNDATE")
    private Date txnDate;
    @JsonProperty("GATEWAYNAME")
    private String gatewayName;
    @JsonProperty("BANKNAME")
    private String bankName;
    @JsonProperty("PAYMENTMODE")
    private String paymentMode;
    @JsonProperty("REFUNDAMT")
    private BigDecimal refundAmt;
    @JsonProperty("MERC_UNQ_REF")
    private String merchantUniqueReference;
    @JsonProperty("UDF_1")
    private String userDefinedParams;

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getTxnId() {
        return txnId;
    }

    public void setTxnId(String txnId) {
        this.txnId = txnId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getBankTxnId() {
        return bankTxnId;
    }

    public void setBankTxnId(String bankTxnId) {
        this.bankTxnId = bankTxnId;
    }

    public BigDecimal getTxnAmount() {
        return txnAmount;
    }

    public void setTxnAmount(BigDecimal txnAmount) {
        this.txnAmount = txnAmount;
    }

    public String getTxnType() {
        return txnType;
    }

    public void setTxnType(String txnType) {
        this.txnType = txnType;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespMsg() {
        return respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    public Date getTxnDate() {
        return txnDate;
    }

    public void setTxnDate(Date txnDate) {
        this.txnDate = txnDate;
    }

    public String getGatewayName() {
        return gatewayName;
    }

    public void setGatewayName(String gatewayName) {
        this.gatewayName = gatewayName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public BigDecimal getRefundAmt() {
        return refundAmt;
    }

    public void setRefundAmt(BigDecimal refundAmt) {
        this.refundAmt = refundAmt;
    }

    public String getMerchantUniqueReference() {
        return merchantUniqueReference;
    }

    public void setMerchantUniqueReference(String merchantUniqueReference) {
        this.merchantUniqueReference = merchantUniqueReference;
    }

    public String getUserDefinedParams() {
        return userDefinedParams;
    }

    public void setUserDefinedParams(String userDefinedParams) {
        this.userDefinedParams = userDefinedParams;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getStatus() {
        return status;
    }

}
