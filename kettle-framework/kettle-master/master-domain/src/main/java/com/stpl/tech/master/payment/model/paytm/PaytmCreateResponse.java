package com.stpl.tech.master.payment.model.paytm;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentResponse;

public class PaytmCreateResponse implements Serializable, PaymentResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3082087326625954491L;
	@JsonProperty("MID")
	private String mid;
	@JsonProperty("ORDERID")
	private String orderId;
	@JsonProperty("TXNAMOUNT")
	private String transactionAmount;
	@JsonProperty("CURRENCY")
	private String currency;
	@JsonProperty("RESPMSG")
	private String responseMessage;
	@JsonProperty("TXNDATE")
	private String transactionDate;
	@JsonProperty("GATEWAYNAME")
	private String gatewayName;
	@JsonProperty("BANKNAME")
	private String bankName;
	@JsonProperty("PAYMENTMODE")
	private String paymentMode;
	@JsonProperty("PROMO_CAMP_ID")
	private String promoCode;
	@JsonProperty("PROMO_STATUS")
	private String promoStatus;
	@JsonProperty("TXNID")
	private String transactionId;
	@JsonProperty("STATUS")
	private String status;
	@JsonProperty("BANKTXNID")
	private String bankTransactionId;
	@JsonProperty("RESPCODE")
	private String responseCode;
	@JsonProperty("CHECKSUMHASH")
	private String checksumHash;
	@JsonProperty("PROMO_RESPCODE")
	private String promoResponseCode;


	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(String transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getResponseMessage() {
		return responseMessage;
	}

	public void setResponseMessage(String responseMessage) {
		this.responseMessage = responseMessage;
	}

	public String getTransactionDate() {
		return transactionDate;
	}

	public void setTransactionDate(String transactionDate) {
		this.transactionDate = transactionDate;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getPaymentMode() {
		return paymentMode;
	}

	public void setPaymentMode(String paymentMode) {
		this.paymentMode = paymentMode;
	}

	public String getPromoCode() {
		return promoCode;
	}

	public void setPromoCode(String promoCode) {
		this.promoCode = promoCode;
	}

	public String getPromoStatus() {
		return promoStatus;
	}

	public void setPromoStatus(String promoStatus) {
		this.promoStatus = promoStatus;
	}

	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getBankTransactionId() {
		return bankTransactionId;
	}

	public void setBankTransactionId(String bankTransactionId) {
		this.bankTransactionId = bankTransactionId;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getChecksumHash() {
		return checksumHash;
	}

	public void setChecksumHash(String checksumHash) {
		this.checksumHash = checksumHash;
	}

	public String getPromoResponseCode() {
		return promoResponseCode;
	}

	public void setPromoResponseCode(String promoResponseCode) {
		this.promoResponseCode = promoResponseCode;
	}

	public TreeMap<String, String> getParameters(String mid) {
		TreeMap<String, String> parameters = new TreeMap<String, String>();
		parameters.put("MID", mid);
		parameters.put("ORDERID", getOrderId());
		parameters.put("TXNAMOUNT", getTransactionAmount());
		parameters.put("TXNID", getTransactionId());
		if(getBankTransactionId()!=null){
            parameters.put("BANKTXNID", getBankTransactionId());
        }
		parameters.put("STATUS", getStatus());
		parameters.put("RESPCODE", getResponseCode());
		parameters.putAll(getPersistentAttributes());
		return parameters;
	}

	@Override
	public Map<String, String> getPersistentAttributes() {
		HashMap<String, String> parameters = new HashMap<String, String>();
		parameters.put("CURRENCY", getCurrency());
		parameters.put("RESPMSG", "" + getResponseMessage());
		parameters.put("TXNDATE",getTransactionDate());
		parameters.put("GATEWAYNAME", getGatewayName());
		parameters.put("BANKNAME", getBankName());
		parameters.put("PAYMENTMODE", getPaymentMode());

		/*
		parameters.put("PROMO_CAMP_ID", getPromoCode());
		parameters.put("PROMO_STATUS", getPromoStatus());
		parameters.put("TXNID", getTransactionId());
		parameters.put("STATUS", getStatus());
		parameters.put("BANKTXNID", getBankTransactionId());
		parameters.put("RESPCODE", getResponseCode());
		parameters.put("RES_CHECKSUMHASH", getChecksumHash());
		parameters.put("PROMO_RESPCODE", getPromoResponseCode());
		*/
		return parameters;
	}

	@Override
	public String getReason() {
		return responseMessage;
	}

	@Override
	public String getPartnerOrderId() {
		return orderId;
	}



}
