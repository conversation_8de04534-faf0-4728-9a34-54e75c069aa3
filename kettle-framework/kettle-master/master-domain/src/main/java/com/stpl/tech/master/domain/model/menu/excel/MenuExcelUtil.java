package com.stpl.tech.master.domain.model.menu.excel;

import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.util.AppConstants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MenuExcelUtil {
    public static List<String> getAllDaySlots(){
        return new ArrayList<>(Arrays.asList(MenuType.DAY_SLOT_BREAKFAST.name(),MenuType.DAY_SLOT_LUNCH.name(),MenuType.DAY_SLOT_EVENING.name(),
                MenuType.DAY_SLOT_DINNER.name()
                ,MenuType.DAY_SLOT_POST_DINNER.name(),MenuType.DAY_SLOT_OVERNIGHT.name(), MenuType.DEFAULT.name(),
                MenuType.DAY_SLOT_ALL.name()));
    }

    public static List<Integer> getAcceptableBrandIds(){
        return new ArrayList<>(Arrays.asList(AppConstants.CHAAYOS_BRAND_ID , AppConstants.GNT_BRAND_ID,AppConstants.DOHFUL_BRAND_ID
        ,AppConstants.SWIGGY_INSTAMART_BRAND_ID));
    }

    public static List<Integer> getAcceptablePartnerIdsMapByMenuApp(String menuApp){
        Map<String,List<Integer>> acceptablePartnerIdsMap  =new HashMap<>();
        acceptablePartnerIdsMap.put(MenuApp.CHANNEL_PARTNER.name(),new ArrayList<>(Arrays.asList(AppConstants.CHANNEL_PARTNER_SWIGGY,AppConstants.CHANNEL_PARTNER_ZOMATO
        ,AppConstants.CHANNEL_PARTNER_MAGICPIN)));
        acceptablePartnerIdsMap.put(MenuApp.KETTLE.name(), new ArrayList<>(Arrays.asList(AppConstants.DINE_IN_CHANNEL_PARTNER)));
        acceptablePartnerIdsMap.put(MenuApp.DINE_IN_APP.name(), new ArrayList<>(Arrays.asList(AppConstants.CHANNEL_PARTNER_DINE_IN_APP,AppConstants.CHANNEL_PARTNER_WEB_APP)));
        return acceptablePartnerIdsMap.get(menuApp);
    }

    public static List<Integer> getAcceptablePartnerIds(){
        return new ArrayList<>(Arrays.asList(AppConstants.CHANNEL_PARTNER_SWIGGY,AppConstants.CHANNEL_PARTNER_ZOMATO,
                AppConstants.CHANNEL_PARTNER_DINE_IN_APP,AppConstants.CHANNEL_PARTNER_WEB_APP,AppConstants.DINE_IN_CHANNEL_PARTNER,
                AppConstants.CHANNEL_PARTNER_MAGICPIN));
    }

    public static List<String> getAcceptableUpdateValues(){
        return new ArrayList<>(Arrays.asList(AppConstants.YES,AppConstants.NO));
    }

    public static List<String> getAcceptableStatusValues(){
        return new ArrayList<>(Arrays.asList(AppConstants.ACTIVE,AppConstants.IN_ACTIVE));
    }

    public static List<String> getAcceptableMenuApps(){
        return new ArrayList<>(Arrays.asList(MenuApp.CHANNEL_PARTNER.name(),MenuApp.KETTLE.name(),MenuApp.DINE_IN_APP.name()));
    }

    public static String getUpdateFieldName(){
        return UPDATE_FIELD_NAME;
    }

    public static String UPDATE_FIELD_NAME = "UPDATE";
    public static String NEW_CHANGE_LOG = "NEW_CHANGES_LOG";
    public static String UPDATE_CHANGE_LOG = "UPDATE_CHANGES_LOG";

    public static String MENU_EXCEL_SEQUENCE = "MENU_EXCEL_SEQUENCE";
    public static String MENU_SEQUENCE = "MENU_SEQUENCE";
    public static String MENU_SEQUENCE_SUB_CAT_MAPPING = "MENU_SEQUENCE_SUB_CAT_MAPPING";
}
