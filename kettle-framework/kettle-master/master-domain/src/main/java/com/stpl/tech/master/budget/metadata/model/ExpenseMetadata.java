/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 12:10:26 PM IST 
//

package com.stpl.tech.master.budget.metadata.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExpenseMetadata", propOrder = {"id", "type", "desc", "accountable", "budgetCat"})
public class ExpenseMetadata implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 7184567533354346591L;
    protected int id;
    @XmlElement(required = true)
    protected String type;
    @XmlElement(required = true)
    protected String desc;
    protected boolean accountable;
    protected String budgetCat;
    protected List<ExpenseValidation> validations;

    private String isInternalExpense;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String categoty) {
        this.type = categoty;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String description) {
        this.desc = description;
    }

    public boolean isAccountable() {
        return accountable;
    }

    public void setAccountable(boolean accountable) {
        this.accountable = accountable;
    }

    public String getBudgetCat() {
        return budgetCat;
    }

    public void setBudgetCat(String budgetCat) {
        this.budgetCat = budgetCat;
    }

    public List<ExpenseValidation> getValidations() {
        if(validations == null) {
            validations = new ArrayList<>();
        }
        return validations;
    }

    public void setValidations(List<ExpenseValidation> validations) {
        this.validations = validations;
    }

    public String getIsInternalExpense() {
        return isInternalExpense;
    }
    public void setIsInternalExpense(String isInternalExpense) {
        this.isInternalExpense = isInternalExpense;
    }

}