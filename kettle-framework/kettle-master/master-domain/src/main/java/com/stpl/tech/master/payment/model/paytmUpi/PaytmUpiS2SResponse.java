package com.stpl.tech.master.payment.model.paytmUpi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentResponse;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class PaytmUpiS2SResponse implements PaymentResponse {
    @JsonProperty("GATEWAYNAME")
    private String gatewayName;
    @JsonProperty("PAYMENTMODE")
    private String paymentMode;
    @JsonProperty("TXNDATE")
    private Date txnDate;
    @JsonProperty("TXNDATETIME")
    private Date txnDateTime;
    @JsonProperty("CUSTID")
    private String custId;
    @JsonProperty("STATUS")
    private String status;
    @JsonProperty("MID")
    private String mid;
    @JsonProperty("ORDERID")
    private String orderId;
    @JsonProperty("CURRENCY")
    private String currency;
    @JsonProperty("TXNID")
    private String txnId;
    @JsonProperty("TXNAMOUNT")
    private BigDecimal txnAmount;
    @JsonProperty("BANKTXNID")
    private String bankTxnId;
    @JsonProperty("BANKNAME")
    private String bankName;
    @JsonProperty("RESPMSG")
    private String respMsg;
    @JsonProperty("RESPCODE")
    private String respCode;
    @JsonProperty("ENC_DATA")
    private String encData;
    @JsonProperty("maskedCardNo")
    private String maskedCardNo;
    @JsonProperty("cardIndexNo")
    private String cardIndexNo;
    @JsonProperty("CHECKSUMHASH")
    private String checkSumHash;
    @JsonProperty("TXNTYPE")
    private String txnType;
    @JsonProperty("REFUNDAMT")
    private BigDecimal refundAmt;
    @JsonProperty("Masked_customer_mobile_numbe")
    private String maskedCustomerMobileNumber;
    @JsonProperty("POS_ID")
    private String posId;
    @JsonProperty("uniqueReferenceLabel")
    private String uniqueReferenceLabel;
    @JsonProperty("uniqueReferenceValue")
    private String uniqueReferenceValue;
    @JsonProperty("pccCode")
    private String pccCode;
    @JsonProperty("PRN")
    private String prn;
    @JsonProperty("udf_1")
    private String udf_1;
    @JsonProperty("udf_2")
    private String udf_2;
    @JsonProperty("udf_3")
    private String udf_3;
    @JsonProperty("comments")
    private String comments;
    @JsonProperty("PROMO_RESPCODE")
    private String promoRespCode;
    @JsonProperty("PROMO_STATUS")
    private String promoStatus;
    @JsonProperty("PROMO_CAMP_ID")
    private String promoCampId;
    @JsonProperty("MERC_UNQ_REF")
    private String mercUnqRef;
    @JsonProperty("SUBS_ID")
    private String subsId;
    @JsonProperty("CHILDTXNLIST")
    private String childTxnList;


    public String getGatewayName() {
        return gatewayName;
    }

    public void setGatewayName(String gatewayName) {
        this.gatewayName = gatewayName;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public Date getTxnDate() {
        return txnDate;
    }

    public void setTxnDate(Date txnDate) {
        this.txnDate = txnDate;
    }

    public Date getTxnDateTime() {
        return txnDateTime;
    }

    public void setTxnDateTime(Date txnDateTime) {
        this.txnDateTime = txnDateTime;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getTxnId() {
        return txnId;
    }

    public void setTxnId(String txnId) {
        this.txnId = txnId;
    }

    public BigDecimal getTxnAmount() {
        return txnAmount;
    }

    public void setTxnAmount(BigDecimal txnAmount) {
        this.txnAmount = txnAmount;
    }

    public String getBankTxnId() {
        return bankTxnId;
    }

    public void setBankTxnId(String bankTxnId) {
        this.bankTxnId = bankTxnId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getRespMsg() {
        return respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getEncData() {
        return encData;
    }

    public void setEncData(String encData) {
        this.encData = encData;
    }

    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }

    public String getCardIndexNo() {
        return cardIndexNo;
    }

    public void setCardIndexNo(String cardIndexNo) {
        this.cardIndexNo = cardIndexNo;
    }

    public String getCheckSumHash() {
        return checkSumHash;
    }

    public void setCheckSumHash(String checkSumHash) {
        this.checkSumHash = checkSumHash;
    }

    public String getTxnType() {
        return txnType;
    }

    public void setTxnType(String txnType) {
        this.txnType = txnType;
    }

    public BigDecimal getRefundAmt() {
        return refundAmt;
    }

    public void setRefundAmt(BigDecimal refundAmt) {
        this.refundAmt = refundAmt;
    }

    public String getMaskedCustomerMobileNumber() {
        return maskedCustomerMobileNumber;
    }

    public void setMaskedCustomerMobileNumber(String maskedCustomerMobileNumber) {
        this.maskedCustomerMobileNumber = maskedCustomerMobileNumber;
    }

    public String getPosId() {
        return posId;
    }

    public void setPosId(String posId) {
        this.posId = posId;
    }

    public String getUniqueReferenceLabel() {
        return uniqueReferenceLabel;
    }

    public void setUniqueReferenceLabel(String uniqueReferenceLabel) {
        this.uniqueReferenceLabel = uniqueReferenceLabel;
    }

    public String getUniqueReferenceValue() {
        return uniqueReferenceValue;
    }

    public void setUniqueReferenceValue(String uniqueReferenceValue) {
        this.uniqueReferenceValue = uniqueReferenceValue;
    }

    public String getPccCode() {
        return pccCode;
    }

    public void setPccCode(String pccCode) {
        this.pccCode = pccCode;
    }

    public String getPrn() {
        return prn;
    }

    public void setPrn(String prn) {
        this.prn = prn;
    }

    public String getUdf_1() {
        return udf_1;
    }

    public void setUdf_1(String udf_1) {
        this.udf_1 = udf_1;
    }

    public String getUdf_2() {
        return udf_2;
    }

    public void setUdf_2(String udf_2) {
        this.udf_2 = udf_2;
    }

    public String getUdf_3() {
        return udf_3;
    }

    public void setUdf_3(String udf_3) {
        this.udf_3 = udf_3;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getPromoRespCode() {
        return promoRespCode;
    }

    public void setPromoRespCode(String promoRespCode) {
        this.promoRespCode = promoRespCode;
    }

    public String getPromoStatus() {
        return promoStatus;
    }

    public void setPromoStatus(String promoStatus) {
        this.promoStatus = promoStatus;
    }

    public String getPromoCampId() {
        return promoCampId;
    }

    public void setPromoCampId(String promoCampId) {
        this.promoCampId = promoCampId;
    }

    public String getMercUnqRef() {
        return mercUnqRef;
    }

    public void setMercUnqRef(String mercUnqRef) {
        this.mercUnqRef = mercUnqRef;
    }

    public String getSubsId() {
        return subsId;
    }

    public void setSubsId(String subsId) {
        this.subsId = subsId;
    }

    public String getChildTxnList() {
        return childTxnList;
    }

    public void setChildTxnList(String childTxnList) {
        this.childTxnList = childTxnList;
    }

    @Override
    public Map<String, String> getPersistentAttributes() {
        Map<String, String> attributes = new HashMap<>();
        attributes.put("bankTransactionId", this.bankTxnId);
        attributes.put("merchantTransactionIdentifier", this.txnId);
        attributes.put("merchantUniqueReference", this.mercUnqRef);
        attributes.put("gateway", this.gatewayName);
        attributes.put("bankName", this.bankName);
        return attributes;
    }

    @Override
    public String getOrderId() {
        return orderId;
    }

    @Override
    public String getStatus() {
        return status;
    }

    @Override
    public String getReason() {
        return null;
    }

    @Override
    public String getTransactionId() {
        return txnId;
    }

	@Override
	public String getPartnerOrderId() {
		return orderId;
	}
}
