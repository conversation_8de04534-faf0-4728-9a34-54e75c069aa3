
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "entity", "event", "contains", "payload", "created_at" })
public class RazorPayEventData implements Serializable {
	private final static long serialVersionUID = 2606320119810542916L;

	@JsonProperty("entity")
	public String entity;
	@JsonProperty("event")
	public String event;
	@JsonProperty("account_id")
	public String accountId;
	@JsonProperty("contains")
	public List<String> contains = null;
	@JsonProperty("payload")
	public Payload payload;
	@JsonProperty("created_at")
	public long createdAt;
	
	public BasicTransactionInfo info;

	public String getEntity() {
		return entity;
	}

	public void setEntity(String entity) {
		this.entity = entity;
	}

	public String getEvent() {
		return event;
	}

	public void setEvent(String event) {
		this.event = event;
	}

	public List<String> getContains() {
		return contains;
	}

	public void setContains(List<String> contains) {
		this.contains = contains;
	}

	public Payload getPayload() {
		return payload;
	}

	public void setPayload(Payload payload) {
		this.payload = payload;
	}

	public long getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(long createdAt) {
		this.createdAt = createdAt;
	}

	public BasicTransactionInfo getInfo() {
		return info;
	}

	public void setInfo(BasicTransactionInfo info) {
		this.info = info;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}


	
}
