package com.stpl.tech.master.payment.model.ingenico;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.payment.model.PaymentRequest;

import java.util.HashMap;
import java.util.Map;

public class IngenicoQrRequest implements PaymentRequest {
    IngenicoMerchant merchant;
    Cart cart;
    IngenicoPayment payment;
    IngenicoTransaction transaction;
    CustomerInfo consumer;


    // Getter Methods

    public IngenicoMerchant getMerchant() {
        return merchant;
    }

    public Cart getCart() {
        return cart;
    }

    public IngenicoPayment getPayment() {
        return payment;
    }

    public IngenicoTransaction getTransaction() {
        return transaction;
    }

    public CustomerInfo getConsumer() {
        return consumer;
    }

    // Setter Methods

    public void setMerchant(IngenicoMerchant merchantObject) {
        this.merchant = merchantObject;
    }

    public void setCart(Cart cartObject) {
        this.cart = cartObject;
    }

    public void setPayment(IngenicoPayment paymentObject) {
        this.payment = paymentObject;
    }

    public void setTransaction(IngenicoTransaction transactionObject) {
        this.transaction = transactionObject;
    }

    public void setConsumer(CustomerInfo consumerObject) {
        this.consumer = consumerObject;
    }

    @JsonIgnore
    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @JsonIgnore
    @Override
    public Map<String, String> getPersistentAttributes() {
        Map<String, String> persistentAttrs = new HashMap<>();
        return persistentAttrs;
    }

    @JsonIgnore
    @Override
    public String getStatus() {
        return "INITIATED";
    }

}
