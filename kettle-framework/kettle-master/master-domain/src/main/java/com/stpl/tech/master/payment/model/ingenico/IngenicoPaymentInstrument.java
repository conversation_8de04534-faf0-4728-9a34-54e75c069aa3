package com.stpl.tech.master.payment.model.ingenico;

public class IngenicoPaymentInstrument {
    Expiry expiry;
    private String provider;
    private String iFSC;
    IngenicoPaymentHolderInfo holder;
    private String bIC;
    private String type;
    private String action;
    private String mICR;
    private String verificationCode;
    private String iBAN;
    private String processor;
    Issuance issuance;
    private String alias;
    private String identifier;
    private String token;
    PaymentAuthentication authentication;
    private String subType;
    private String issuer;
    private String acquirer;


    // Getter Methods

    public Expiry getExpiry() {
        return expiry;
    }

    public String getProvider() {
        return provider;
    }

    public String getIFSC() {
        return iFSC;
    }

    public IngenicoPaymentHolderInfo getHolder() {
        return holder;
    }

    public String getBIC() {
        return bIC;
    }

    public String getType() {
        return type;
    }

    public String getAction() {
        return action;
    }

    public String getMICR() {
        return mICR;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public String getIBAN() {
        return iBAN;
    }

    public String getProcessor() {
        return processor;
    }

    public Issuance getIssuance() {
        return issuance;
    }

    public String getAlias() {
        return alias;
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getToken() {
        return token;
    }

    public PaymentAuthentication getAuthentication() {
        return authentication;
    }

    public String getSubType() {
        return subType;
    }

    public String getIssuer() {
        return issuer;
    }

    public String getAcquirer() {
        return acquirer;
    }

    // Setter Methods

    public void setExpiry(Expiry expiryObject) {
        this.expiry = expiryObject;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public void setIFSC(String iFSC) {
        this.iFSC = iFSC;
    }

    public void setHolder(IngenicoPaymentHolderInfo holderObject) {
        this.holder = holderObject;
    }

    public void setBIC(String bIC) {
        this.bIC = bIC;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setMICR(String mICR) {
        this.mICR = mICR;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public void setIBAN(String iBAN) {
        this.iBAN = iBAN;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public void setIssuance(Issuance issuanceObject) {
        this.issuance = issuanceObject;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public void setAuthentication(PaymentAuthentication authenticationObject) {
        this.authentication = authenticationObject;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }
}
