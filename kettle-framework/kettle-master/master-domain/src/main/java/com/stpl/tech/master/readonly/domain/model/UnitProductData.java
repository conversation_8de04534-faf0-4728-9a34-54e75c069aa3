/**
 * 
 */
package com.stpl.tech.master.readonly.domain.model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.domain.model.MenuType;

/**
 * <AUTHOR>
 *
 */
public class UnitProductData {

	protected Collection<ProductVO> products;
	protected Collection<TaxDataVO> taxes;
	protected Map<Integer, List<Integer>>categoryMap;
	protected Map<MenuType, Map<Integer, List<ProductPriceVO>>> priceMap;

	public Map<Integer, List<Integer>> getCategoryMap() {
		return categoryMap;
	}

	public void setCategoryMap(Map<Integer, List<Integer>> categoryMap) {
		this.categoryMap = categoryMap;
	}

	public Collection<TaxDataVO> getTaxes() {
		if (taxes == null) {
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	public Collection<ProductVO> getProducts() {
		if (products == null) {
			products = new ArrayList<>();
		}
		return products;
	}

	public Map<MenuType, Map<Integer, List<ProductPriceVO>>> getPriceMap() {
		return priceMap;
	}

	public void setPriceMap(Map<MenuType, Map<Integer, List<ProductPriceVO>>> priceMap) {
		this.priceMap = priceMap;
	}

	
}
