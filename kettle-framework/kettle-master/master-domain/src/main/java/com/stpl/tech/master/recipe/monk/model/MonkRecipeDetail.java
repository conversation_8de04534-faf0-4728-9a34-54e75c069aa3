//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.monk.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.recipe.model.BasicInfo;
import com.stpl.tech.master.recipe.model.ProductData;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MonkRecipeDetail", propOrder = { "_id", "recipeId", "product", "dimension", "name", "status",
		"creationDate", "modificationDate", "startDate", "endDate", "lastUpdatedById", "lastUpdatedById",
		"lastUpdatedByName" })
@Document(collection = "monkRecipes")
public class MonkRecipeDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2797388652188167256L;
	@Id
	private String _id;
	@Field
	protected int recipeId;
	@Field
	protected ProductData product;
	@Field
	protected BasicInfo dimension;
	@Field
	protected String name;
	@Field
	protected String status = "ACTIVE";
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date creationDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date modificationDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date startDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date endDate;
	@Field
	protected int lastUpdatedById;
	@Field
	protected String lastUpdatedByName;
	@Field
	protected String preparation;
	@Field
	protected List<MonkRecipeData> datas;

	public String get_id() {
		return _id;
	}

	public void set_id(String objectId) {
		this._id = objectId;
	}

	/**
	 * Gets the value of the recipeId property.
	 * 
	 */
	public int getRecipeId() {
		return recipeId;
	}

	/**
	 * Sets the value of the recipeId property.
	 * 
	 */
	public void setRecipeId(int value) {
		this.recipeId = value;
	}

	/**
	 * Gets the value of the product property.
	 * 
	 * @return possible object is {@link ProductData }
	 * 
	 */
	public ProductData getProduct() {
		return product;
	}

	/**
	 * Sets the value of the product property.
	 * 
	 * @param value
	 *            allowed object is {@link ProductData }
	 * 
	 */
	public void setProduct(ProductData value) {
		this.product = value;
	}

	/**
	 * Gets the value of the dimension property.
	 * 
	 * @return possible object is {@link BasicInfo }
	 * 
	 */
	public BasicInfo getDimension() {
		return dimension;
	}

	/**
	 * Sets the value of the dimension property.
	 * 
	 * @param value
	 *            allowed object is {@link BasicInfo }
	 * 
	 */
	public void setDimension(BasicInfo value) {
		this.dimension = value;
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	/**
	 * Gets the value of the creationDate property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getCreationDate() {
		return creationDate;
	}

	/**
	 * Sets the value of the creationDate property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCreationDate(Date value) {
		this.creationDate = value;
	}

	/**
	 * Gets the value of the modificationDate property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getModificationDate() {
		return modificationDate;
	}

	/**
	 * Sets the value of the modificationDate property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setModificationDate(Date value) {
		this.modificationDate = value;
	}

	/**
	 * Gets the value of the startDate property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * Sets the value of the startDate property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStartDate(Date value) {
		this.startDate = value;
	}

	/**
	 * Gets the value of the endDate property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * Sets the value of the endDate property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	public int getLastUpdatedById() {
		return lastUpdatedById;
	}

	public void setLastUpdatedById(int lastUpdatedById) {
		this.lastUpdatedById = lastUpdatedById;
	}

	public String getLastUpdatedByName() {
		return lastUpdatedByName;
	}

	public void setLastUpdatedByName(String lastUpdatedByName) {
		this.lastUpdatedByName = lastUpdatedByName;
	}

	public List<MonkRecipeData> getDatas() {
		return datas;
	}

	public void setDatas(List<MonkRecipeData> datas) {
		this.datas = datas;
	}

	public String getPreparation() {
		return preparation;
	}

	public void setPreparation(String preperation) {
		this.preparation = preperation;
	}

	/**
	 * 
	 */
	public void process(int mode) {
		if (datas != null && datas.size() > 0) {
			for (MonkRecipeData data : datas) {
				data.setProductName(this.getProduct().getName());
				data.setProductId(this.getProduct().getProductId());
				data.setDimension(this.getDimension().getCode());
				data.setKey(data.getKeyString());
				data.setContent(data.getContentString(mode));
			}
		}
	}

}
