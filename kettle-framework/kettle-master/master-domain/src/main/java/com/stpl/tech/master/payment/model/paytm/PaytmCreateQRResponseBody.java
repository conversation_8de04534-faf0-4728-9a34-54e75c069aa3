
package com.stpl.tech.master.payment.model.paytm;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "resultInfo",
    "qrCodeId",
    "qrData",
    "image"
})
public class PaytmCreateQRResponseBody {

    @JsonProperty("resultInfo")
    private ResultInfo resultInfo;
    @JsonProperty("qrCodeId")
    private String qrCodeId;
    @JsonProperty("qrData")
    private String qrData;
    @JsonProperty("image")
    private String image;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("resultInfo")
    public ResultInfo getResultInfo() {
        return resultInfo;
    }

    @JsonProperty("resultInfo")
    public void setResultInfo(ResultInfo resultInfo) {
        this.resultInfo = resultInfo;
    }

    @JsonProperty("qrCodeId")
    public String getQrCodeId() {
        return qrCodeId;
    }

    @JsonProperty("qrCodeId")
    public void setQrCodeId(String qrCodeId) {
        this.qrCodeId = qrCodeId;
    }

    @JsonProperty("qrData")
    public String getQrData() {
        return qrData;
    }

    @JsonProperty("qrData")
    public void setQrData(String qrData) {
        this.qrData = qrData;
    }

    @JsonProperty("image")
    public String getImage() {
        return image;
    }

    @JsonProperty("image")
    public void setImage(String image) {
        this.image = image;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("resultInfo", resultInfo).append("qrCodeId", qrCodeId).append("qrData", qrData).append("image", image).append("additionalProperties", additionalProperties).toString();
    }

}
