package com.stpl.tech.master.payment.model.ingenico;

public class IngenicoPaymentMethod {
    private String token;
    private String instrumentAliasName = null;
    private String instrumentToken = null;
    private String bankSelectionCode;
    private String paymentMode;
    private PaymentMethodAcs aCS;
    private String oTP = null;
    IngenicoPaymentTransaction paymentTransaction;
    private String authentication;
    Error error;


    // Getter Methods

    public String getToken() {
        return token;
    }

    public String getInstrumentAliasName() {
        return instrumentAliasName;
    }

    public String getInstrumentToken() {
        return instrumentToken;
    }

    public String getBankSelectionCode() {
        return bankSelectionCode;
    }

    public PaymentMethodAcs getACS() {
        return aCS;
    }

    public String getOTP() {
        return oTP;
    }

    public IngenicoPaymentTransaction getPaymentTransaction() {
        return paymentTransaction;
    }

    public String getAuthentication() {
        return authentication;
    }

    public Error getError() {
        return error;
    }

    // Setter Methods

    public void setToken(String token) {
        this.token = token;
    }

    public void setInstrumentAliasName(String instrumentAliasName) {
        this.instrumentAliasName = instrumentAliasName;
    }

    public void setInstrumentToken(String instrumentToken) {
        this.instrumentToken = instrumentToken;
    }

    public void setBankSelectionCode(String bankSelectionCode) {
        this.bankSelectionCode = bankSelectionCode;
    }

    public void setACS(PaymentMethodAcs aCS) {
        this.aCS = aCS;
    }

    public void setOTP(String oTP) {
        this.oTP = oTP;
    }

    public void setPaymentTransaction(IngenicoPaymentTransaction paymentTransactionObject) {
        this.paymentTransaction = paymentTransactionObject;
    }

    public void setAuthentication(String authentication) {
        this.authentication = authentication;
    }

    public void setError(Error error) {
        this.error = error;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }
}
