/**
 * 
 */
package com.stpl.tech.master.recipe.monk.model;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import com.stpl.tech.util.AppConstants;
import io.opencensus.trace.Link;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MonkRecipeData", propOrder = { "_id", "productId", "productName", "dimension", "quantity", "prep",
		"water", "milk", "boilSettle", "noOfBoils", "heatingTimeMins", "heatingTimeSecs",
		"key" ,"content"})
@Document(collection = "monkRecipeData")
public class MonkRecipeData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8307191926636440910L;
	@Id
	private String _id;
	@Field
	protected int productId;
	@Field
	protected String productName;
	@Field
	protected String dimension;
	@Field
	protected int quantity;
	@Field
	protected String prep;
	@Field
	protected int water;
	@Field
	protected int milk;
	@Field
	protected int boilSettle;
	@Field
	protected int noOfBoils;
	@Field
	protected int heatingTimeMins;
	@Field
	protected int heatingTimeSecs;
	@Field
	protected String key;
	@Field
	protected String content;
	@Field
	protected String monkVersion = AppConstants.CHAI_MONK_DEFAULT_VERSION;
	@Field
	protected String milkVariantMonkVersion = AppConstants.CHAI_MONK_DEFAULT_VERSION;
	@Field
	protected LinkedHashMap<String, Integer> addons;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public String getPrep() {
		return prep;
	}

	public void setPrep(String prep) {
		this.prep = prep;
	}

	public int getWater() {
		return water;
	}

	public void setWater(int water) {
		this.water = water;
	}

	public int getMilk() {
		return milk;
	}

	public void setMilk(int milk) {
		this.milk = milk;
	}

	public int getBoilSettle() {
		return boilSettle;
	}

	public void setBoilSettle(int boilSettle) {
		this.boilSettle = boilSettle;
	}

	public int getNoOfBoils() {
		return noOfBoils;
	}

	public void setNoOfBoils(int noOfBoils) {
		this.noOfBoils = noOfBoils;
	}

	public int getHeatingTimeMins() {
		return heatingTimeMins;
	}

	public void setHeatingTimeMins(int heatingTimeMins) {
		this.heatingTimeMins = heatingTimeMins;
	}

	public int getHeatingTimeSecs() {
		return heatingTimeSecs;
	}

	public void setHeatingTimeSecs(int heatingTimeSecs) {
		this.heatingTimeSecs = heatingTimeSecs;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getMonkVersion() {
		return monkVersion;
	}

	public void setMonkVersion(String monkVersion) {
		this.monkVersion = monkVersion;
	}

	public String getMilkVariantMonkVersion() {
		return milkVariantMonkVersion;
	}

	public void setMilkVariantMonkVersion(String milkVariantMonkVersion) {
		this.milkVariantMonkVersion = milkVariantMonkVersion;
	}

	public LinkedHashMap<String, Integer> getAddons() {
		return addons;
	}

	public void setAddons(LinkedHashMap<String, Integer> addons) {
		this.addons = addons;
	}

	String getKeyString() {
		StringBuffer b = new StringBuffer();
		b.append(this.getProductId());
		b.append("#");
		b.append(this.getDimension());
		b.append("#");
		b.append(this.getQuantity());
		b.append("$");
		return b.toString();
	}

	String getContentString(int mode) {
		StringBuffer b = new StringBuffer(this.getPrep());
		b.append(mode);
		b.append(StringUtils.leftPad(this.getWater() + "", 4, "0"));
		b.append(StringUtils.leftPad(this.getMilk() + "", 4, "0"));
		b.append(StringUtils.leftPad(this.getBoilSettle() + "", 2, "0"));
		b.append(StringUtils.leftPad(this.getNoOfBoils() + "", 1, "0"));
		b.append(StringUtils.leftPad(this.getHeatingTimeMins() + "", 2, "0"));
		b.append(StringUtils.leftPad(this.getHeatingTimeSecs() + "", 2, "0"));
		b.append(StringUtils.leftPad("", 7, "0"));
		b.append("$E");
		return b.toString();
	}

	/**
	 * @param monkRecipeData
	 */
	public void copy(MonkRecipeData d) {
		this.boilSettle = d.boilSettle;
		this.content = d.content;
		this.dimension = d.dimension;
		this.heatingTimeMins = d.heatingTimeMins;
		this.heatingTimeSecs = d.heatingTimeSecs;
		this.key = d.key;
		this.milk = d.milk;
		this.noOfBoils = d.noOfBoils;
		this.prep = d.prep;
		this.productId = d.productId;
		this.quantity = d.quantity;
		this.water = d.water;
		this.productName = d.productName;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + boilSettle;
		result = prime * result + ((dimension == null) ? 0 : dimension.hashCode());
		result = prime * result + heatingTimeMins;
		result = prime * result + heatingTimeSecs;
		result = prime * result + milk;
		result = prime * result + noOfBoils;
		result = prime * result + ((prep == null) ? 0 : prep.hashCode());
		result = prime * result + productId;
		result = prime * result + quantity;
		result = prime * result + water;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MonkRecipeData other = (MonkRecipeData) obj;
		if (boilSettle != other.boilSettle)
			return false;
		if (dimension == null) {
			if (other.dimension != null)
				return false;
		} else if (!dimension.equals(other.dimension))
			return false;
		if (heatingTimeMins != other.heatingTimeMins)
			return false;
		if (heatingTimeSecs != other.heatingTimeSecs)
			return false;
		if (milk != other.milk)
			return false;
		if (noOfBoils != other.noOfBoils)
			return false;
		if (prep == null) {
			if (other.prep != null)
				return false;
		} else if (!prep.equals(other.prep))
			return false;
		if (productId != other.productId)
			return false;
		if (quantity != other.quantity)
			return false;
		if (water != other.water)
			return false;
		return true;
	}

}
