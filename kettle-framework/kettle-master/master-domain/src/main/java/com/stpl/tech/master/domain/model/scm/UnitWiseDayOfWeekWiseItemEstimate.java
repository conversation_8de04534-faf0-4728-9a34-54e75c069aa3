package com.stpl.tech.master.domain.model.scm;

import java.util.HashMap;
import java.util.Map;

public class UnitWiseDayOfWeekWiseItemEstimate {

	private int unitId;
	private Map<Integer, Map<Integer, Map<String, UnitDayWiseItemData>>> estimates = new HashMap<>();

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public Map<Integer, Map<Integer, Map<String, UnitDayWiseItemData>>> getEstimates() {
		return estimates;
	}

	public void setEstimates(Map<Integer, Map<Integer, Map<String, UnitDayWiseItemData>>> estimates) {
		this.estimates = estimates;
	}

	
}
