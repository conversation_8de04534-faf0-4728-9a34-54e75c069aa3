
package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "customer_name", "customer_email", "customer_contact", "customer_address" })
public class CustomerDetails implements Serializable {

	private final static long serialVersionUID = -1989073918743020916L;
	@JsonProperty("customer_name")
	public String customerName;
	@JsonProperty("customer_email")
	public String customerEmail;
	@JsonProperty("customer_contact")
	public String customerContact;
	@JsonProperty("customer_address")
	public String customerAddress;

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerEmail() {
		return customerEmail;
	}

	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}

	public String getCustomerContact() {
		return customerContact;
	}

	public void setCustomerContact(String customerContact) {
		this.customerContact = customerContact;
	}

	public String getCustomerAddress() {
		return customerAddress;
	}

	public void setCustomerAddress(String customerAddress) {
		this.customerAddress = customerAddress;
	}

}
