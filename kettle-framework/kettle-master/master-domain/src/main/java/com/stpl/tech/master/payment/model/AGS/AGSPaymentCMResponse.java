package com.stpl.tech.master.payment.model.AGS;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class AGSPaymentCMResponse implements Serializable {

    private static final long serialVersionUID = -5054714510504785713L;

    @JsonProperty("status")
    private boolean status;

    @JsonProperty("code")
    private Integer code;

    @JsonProperty("title")
    private String title;

    @JsonProperty("msg")
    private String msg;

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
