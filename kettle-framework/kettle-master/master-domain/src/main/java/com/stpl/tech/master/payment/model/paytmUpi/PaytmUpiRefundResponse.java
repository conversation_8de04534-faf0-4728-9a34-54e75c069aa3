package com.stpl.tech.master.payment.model.paytmUpi;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PaytmUpiRefundResponse {
    @JsonProperty("MID")
    private String mid;
    @JsonProperty("REFID")
    private String refId;
    @JsonProperty("TXNID")
    private String txnId;
    @JsonProperty("ORDERID")
    private String orderId;
    @JsonProperty("REFUNDAMOUNT")
    private String refundAmount;
    @JsonProperty("TXNAMOUNT")
    private String txnAmount;
    @JsonProperty("STATUS")
    private String status;
    @JsonProperty("RESPCODE")
    private String respCode;
    @JsonProperty("RESPMSG")
    private String respMsg;
    @JsonProperty("TXNDATE")
    private String txnDate;
    @JsonProperty("GATEWAYNAME")
    private String gatewayName;
    @JsonProperty("CARD_ISSUER")
    private String cardIssuer;
    @JsonProperty("PAYMENTMODE")
    private String paymentMode;
    @JsonProperty("REFUNDDATE")
    private String refundDate;
    @JsonProperty("REFUNDID")
    private String refundId;
    @JsonProperty("BANKTXNID")
    private String bankTxnId;
    @JsonProperty("TOTALREFUNDAMT")
    private String totalRefundAmt;
    @JsonProperty("GATEWAY")
    private String gateway;

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getTxnId() {
        return txnId;
    }

    public void setTxnId(String txnId) {
        this.txnId = txnId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getTxnAmount() {
        return txnAmount;
    }

    public void setTxnAmount(String txnAmount) {
        this.txnAmount = txnAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespMsg() {
        return respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    public String getTxnDate() {
        return txnDate;
    }

    public void setTxnDate(String txnDate) {
        this.txnDate = txnDate;
    }

    public String getGatewayName() {
        return gatewayName;
    }

    public void setGatewayName(String gatewayName) {
        this.gatewayName = gatewayName;
    }

    public String getCardIssuer() {
        return cardIssuer;
    }

    public void setCardIssuer(String cardIssuer) {
        this.cardIssuer = cardIssuer;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public String getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(String refundDate) {
        this.refundDate = refundDate;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getBankTxnId() {
        return bankTxnId;
    }

    public void setBankTxnId(String bankTxnId) {
        this.bankTxnId = bankTxnId;
    }

    public String getTotalRefundAmt() {
        return totalRefundAmt;
    }

    public void setTotalRefundAmt(String totalRefundAmt) {
        this.totalRefundAmt = totalRefundAmt;
    }
}
