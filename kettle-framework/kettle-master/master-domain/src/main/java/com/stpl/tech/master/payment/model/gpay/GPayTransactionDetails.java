package com.stpl.tech.master.payment.model.gpay;

import java.io.Serializable;

public class GPayTransactionDetails implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2593630281628135383L;
	private String transactionId;
	private GPayAmount amount;
	private String description;

	
	public GPayTransactionDetails() {
		super();
	}

	public GPayTransactionDetails(String transactionId, GPayAmount amount, String description) {
		super();
		this.transactionId = transactionId;
		this.amount = amount;
		this.description = description;
	}

	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public GPayAmount getAmount() {
		return amount;
	}

	public void setAmount(GPayAmount amount) {
		this.amount = amount;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
