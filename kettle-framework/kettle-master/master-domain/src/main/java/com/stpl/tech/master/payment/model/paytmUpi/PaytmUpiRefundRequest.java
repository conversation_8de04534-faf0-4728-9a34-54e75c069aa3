package com.stpl.tech.master.payment.model.paytmUpi;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PaytmUpiRefundRequest {
    @JsonProperty("MID")
    private String mid;
    @JsonProperty("REFID")
    private String refId;
    @JsonProperty("TXNID")
    private String txnId;
    @JsonProperty("ORDERID")
    private String orderId;
    @JsonProperty("REFUNDAMOUNT")
    private String refundAmount;
    @JsonProperty("TXNTYPE")
    private String txnType;
    @JsonProperty("CHECKSUM")
    private String checksum;
    @JsonProperty("CHECKSUMHASH")
    private String checksumHash;
    @JsonProperty("COMMENTS")
    private String comments;

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getTxnId() {
        return txnId;
    }

    public void setTxnId(String txnId) {
        this.txnId = txnId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getTxnType() {
        return txnType;
    }

    public void setTxnType(String txnType) {
        this.txnType = txnType;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
}
