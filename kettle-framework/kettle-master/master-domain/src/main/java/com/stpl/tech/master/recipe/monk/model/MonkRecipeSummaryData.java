/**
 * 
 */
package com.stpl.tech.master.recipe.monk.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MonkRecipeSummaryData", propOrder = { "_id" })
@Document
public class MonkRecipeSummaryData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -384265821348893491L;
	@Id
	private String _id;
	@Field
	protected int added;
	@Field
	protected int updated;
	@Field
	protected int removed;
	@Field
	protected List<String> additions;
	@Field
	protected List<String> updations;
	@Field
	protected List<String> deletions;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public int getAdded() {
		return added;
	}

	public void setAdded(int added) {
		this.added = added;
	}

	public int getUpdated() {
		return updated;
	}

	public void setUpdated(int updated) {
		this.updated = updated;
	}

	public int getRemoved() {
		return removed;
	}

	public void setRemoved(int removed) {
		this.removed = removed;
	}

	public List<String> getAdditions() {
		if (additions == null) {
			additions = new ArrayList<String>();
		}
		return additions;
	}

	public void setAdditions(List<String> additions) {
		this.additions = additions;
	}

	public List<String> getUpdations() {
		if (updations == null) {
			updations = new ArrayList<String>();
		}
		return updations;
	}

	public void setUpdations(List<String> updations) {
		this.updations = updations;
	}

	public List<String> getDeletions() {
		if (deletions == null) {
			deletions = new ArrayList<String>();
		}
		return deletions;
	}

	public void setDeletions(List<String> deletions) {
		this.deletions = deletions;
	}

}