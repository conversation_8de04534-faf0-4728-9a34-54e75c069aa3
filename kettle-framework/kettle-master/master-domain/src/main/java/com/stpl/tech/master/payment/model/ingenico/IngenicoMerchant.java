package com.stpl.tech.master.payment.model.ingenico;

public class IngenicoMerchant {
    private String webhookEndpointURL;
    private String responseType;
    private String responseEndpointURL;
    private String description;
    private String identifier;
    private String webhookType;


    // Getter Methods

    public String getWebhookEndpointURL() {
        return webhookEndpointURL;
    }

    public String getResponseType() {
        return responseType;
    }

    public String getResponseEndpointURL() {
        return responseEndpointURL;
    }

    public String getDescription() {
        return description;
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getWebhookType() {
        return webhookType;
    }

    // Setter Methods

    public void setWebhookEndpointURL(String webhookEndpointURL) {
        this.webhookEndpointURL = webhookEndpointURL;
    }

    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }

    public void setResponseEndpointURL(String responseEndpointURL) {
        this.responseEndpointURL = responseEndpointURL;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public void setWebhookType(String webhookType) {
        this.webhookType = webhookType;
    }
}
