/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.18 at 03:33:20 PM IST 
//

package com.stpl.tech.master.readonly.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import com.stpl.tech.master.domain.model.IdCodeNameValue;
import org.apache.commons.lang.builder.CompareToBuilder;

import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Product", propOrder = { "id", "name", "description", "hasSizeProfile", "hasAddons", "type", "subType",
		"webType", "attribute", "classification", "shortCode", "inventoryTracked", "employeeMealComponent", "prices",
		"taxCode", "customize" ,"brandId"})
public class ProductVO implements Serializable, Comparable<ProductVO>, Cloneable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6658784005122791085L;
	protected int id;
	protected String name;
	protected String productAliasName;
	protected String description;
	protected boolean hasSizeProfile;
	protected boolean hasAddons;
	protected int type;
	protected int subType;
	protected Integer webType;
	@XmlElement(required = true)
	protected String attribute;
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	protected ProductClassification classification;
	@XmlElement(required = true)
	protected String shortCode;
	protected boolean inventoryTracked;
	protected boolean employeeMealComponent;
	@XmlElement(required = true)
	protected List<ProductPriceVO> prices;
	@XmlElement(required = true)
	protected String taxCode;
	protected boolean customize;
	// TODO Mohit To Be Removed
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected BillType billType;
	protected BigDecimal prepTime;
	protected Integer brandId;
	protected String skuCode;
	protected boolean isMilkBasedProduct;
	protected Set<IdCodeNameValue> regularTags = new HashSet<>();
	protected Set<IdCodeNameValue> nutritionTags = new HashSet<>();
	protected String serviceChargeApplicable;
	protected int stationCategory;
	protected String stationCategoryName;

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public ProductVO() {

	}



	public ProductVO(Product p) {
		this.attribute = p.getAttribute();
		this.classification = p.getClassification();
		this.customize = p.isCustomize();
		this.description = p.getDescription();
		this.employeeMealComponent = p.isEmployeeMealComponent();
		this.hasAddons = p.isHasAddons();
		this.hasSizeProfile = p.isHasSizeProfile();
		this.id = p.getId();
		this.inventoryTracked = p.isInventoryTracked();
		this.name = p.getName();
		this.shortCode = p.getShortCode();
		this.subType = p.getSubType();
		this.taxCode = p.getTaxCode();
		this.type = p.getType();
		this.webType = p.getWebType();
		this.billType = p.getBillType();
		this.prepTime = p.getPrepTime();
		this.brandId=p.getBrandId();
		this.skuCode=p.getSkuCode();
		for (com.stpl.tech.master.domain.model.ProductPrice price : p.getPrices()) {
			if (price.getRecipe() != null) {
				this.getPrices().add(new ProductPriceVO(price));
				if(price.getRecipe().isMilkBasedRecipe()){
					this.isMilkBasedProduct = true;
				}
			}
		}
		this.productAliasName = p.getProductAliasName();
		this.regularTags = p.getRegularTags();
		this.nutritionTags = p.getNutritionTags();
		this.serviceChargeApplicable = p.getServiceChargeApplicable();
		this.stationCategory = p.getStationCategory();
		this.stationCategoryName = p.getStationCategoryName();
	}

	/**
	 * Gets the value of the id property.
	 * 
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the description property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Sets the value of the description property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDescription(String value) {
		this.description = value;
	}

	/**
	 * Gets the value of the hasSizeProfile property.
	 * 
	 */
	public boolean isHasSizeProfile() {
		return hasSizeProfile;
	}

	/**
	 * Sets the value of the hasSizeProfile property.
	 * 
	 */
	public void setHasSizeProfile(boolean value) {
		this.hasSizeProfile = value;
	}

	/**
	 * Gets the value of the hasAddons property.
	 * 
	 */
	public boolean isHasAddons() {
		return hasAddons;
	}

	/**
	 * Sets the value of the hasAddons property.
	 * 
	 */
	public void setHasAddons(boolean value) {
		this.hasAddons = value;
	}

	/**
	 * Gets the value of the type property.
	 * 
	 */
	public int getType() {
		return type;
	}

	/**
	 * Sets the value of the type property.
	 * 
	 */
	public void setType(int value) {
		this.type = value;
	}

	/**
	 * Gets the value of the subType property.
	 * 
	 */
	public int getSubType() {
		return subType;
	}

	/**
	 * Sets the value of the webType property.
	 * 
	 */
	public void setWebType(Integer value) {
		this.webType = value;
	}

	/**
	 * Gets the value of the webType property.
	 * 
	 */
	public Integer getWebType() {
		return webType;
	}

	/**
	 * Sets the value of the subType property.
	 * 
	 */
	public void setSubType(int value) {
		this.subType = value;
	}

	/**
	 * Gets the value of the attribute property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAttribute() {
		return attribute;
	}

	/**
	 * Sets the value of the attribute property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAttribute(String value) {
		this.attribute = value;
	}

	/**
	 * Gets the value of the inventoryTracked property.
	 * 
	 */
	public boolean isInventoryTracked() {
		return inventoryTracked;
	}

	/**
	 * Sets the value of the inventoryTracked property.
	 * 
	 */
	public void setInventoryTracked(boolean value) {
		this.inventoryTracked = value;
	}

	public boolean isEmployeeMealComponent() {
		return employeeMealComponent;
	}

	public void setEmployeeMealComponent(boolean employeeMealComponent) {
		this.employeeMealComponent = employeeMealComponent;
	}

	/**
	 * Gets the value of the prices property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the prices property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getPrices().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link ProductPriceVO }
	 * 
	 * 
	 */
	public List<ProductPriceVO> getPrices() {
		if (prices == null) {
			prices = new ArrayList<ProductPriceVO>();
		}
		return this.prices;
	}

	@Override
	public int compareTo(ProductVO o) {
		return new CompareToBuilder().append(this.type, o.type).append(this.subType, o.subType)
				.append(o.attribute, this.attribute).append(this.name, o.name).toComparison();
	}

	public ProductClassification getClassification() {
		return classification;
	}

	public void setClassification(ProductClassification classification) {
		this.classification = classification;
	}

	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	public boolean isCustomize() {
		return customize;
	}

	public void setCustomize(boolean customize) {
		this.customize = customize;
	}

	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	public BillType getBillType() {
		return billType;
	}

	public void setBillType(BillType billType) {
		this.billType = billType;
	}

	public Object clone() throws  CloneNotSupportedException{
		return super.clone();
	}

	public BigDecimal getPrepTime() {
		return prepTime;
	}

	public void setPrepTime(BigDecimal prepTime) {
		this.prepTime = prepTime;
	}

	public String getSkuCode() {
		return skuCode;
	}

	public void setSkuCode(String skuCode) {
		this.skuCode = skuCode;
	}

	public String getProductAliasName() {
		return productAliasName;
	}

	public void setProductAliasName(String productAliasName) {
		this.productAliasName = productAliasName;
	}

	public boolean isMilkBasedProduct() {
		return isMilkBasedProduct;
	}

	public void setMilkBasedProduct(boolean milkBasedProduct) {
		isMilkBasedProduct = milkBasedProduct;
	}

	public Set<IdCodeNameValue> getRegularTags() {
		return regularTags;
	}

	public void setRegularTags(Set<IdCodeNameValue> regularTags) {
		this.regularTags = regularTags;
	}

	public Set<IdCodeNameValue> getNutritionTags() {
		return nutritionTags;
	}

	public void setNutritionTags(Set<IdCodeNameValue> nutritionTags) {
		this.nutritionTags = nutritionTags;
	}

	public String getServiceChargeApplicable() {
		return serviceChargeApplicable;
	}

	public void setServiceChargeApplicable(String serviceChargeApplicable) {
		this.serviceChargeApplicable = serviceChargeApplicable;
	}
}
