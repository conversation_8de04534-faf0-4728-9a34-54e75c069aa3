package com.stpl.tech.master.payment.model.razorpay;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "participant_name", "email", "phone"})
public class PaymentNotes implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -7778546490682389940L;

	
	@JsonProperty("name")
	public String name;
	@JsonProperty("email")
	public String email;
	@JsonProperty("phone")
	public String contact;
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getContact() {
		return contact;
	}
	public void setContact(String contact) {
		this.contact = contact;
	}
	
	
	
}
