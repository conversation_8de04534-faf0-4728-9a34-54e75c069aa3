//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.12 at 05:35:56 PM IST 
//


package com.stpl.tech.master.monk.configuration.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for MonkConfigurationScope.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="MonkConfigurationScope"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="ALL"/&gt;
 *     &lt;enumeration value="MONK1"/&gt;
 *     &lt;enumeration value="MONK2"/&gt;
 *     &lt;enumeration value="MONK3"/&gt;
 *     &lt;enumeration value="MONK4"/&gt;
 *     &lt;enumeration value="MONK5"/&gt;
 *     &lt;enumeration value="MONK6"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "MonkConfigurationScope")
@XmlEnum
public enum MonkConfigurationScope {

    ALL("ALL"),
    @XmlEnumValue("MONK1")
    MONK1("MONK1"),
    @XmlEnumValue("MONK2")
    MONK2("MONK2"),
    @XmlEnumValue("MONK3")
    MONK3("MONK3"),
    @XmlEnumValue("MONK4")
    MONK4("MONK4"),
    @XmlEnumValue("MONK5")
    MONK5("MONK5"),
    @XmlEnumValue("MONK6")
    MONK6("MONK6");
    private final String value;

    MonkConfigurationScope(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static MonkConfigurationScope fromValue(String v) {
        for (MonkConfigurationScope c: MonkConfigurationScope.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
