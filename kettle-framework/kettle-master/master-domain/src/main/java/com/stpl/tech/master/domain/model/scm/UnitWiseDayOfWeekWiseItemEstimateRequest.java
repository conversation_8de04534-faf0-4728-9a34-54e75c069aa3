package com.stpl.tech.master.domain.model.scm;

import java.util.List;

public class UnitWiseDayOfWeekWiseItemEstimateRequest {

	private int unitId;
	private List<Integer> daysOfWeek;

	public UnitWiseDayOfWeekWiseItemEstimateRequest() {
		super();
	}

	public UnitWiseDayOfWeekWiseItemEstimateRequest(int unitId, List<Integer> daysOfWeek) {
		super();
		this.unitId = unitId;
		this.daysOfWeek = daysOfWeek;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public List<Integer> getDaysOfWeek() {
		return daysOfWeek;
	}

	public void setDaysOfWeek(List<Integer> daysOfWeek) {
		this.daysOfWeek = daysOfWeek;
	}

	@Override
	public String toString() {
		return "UnitWiseDayOfWeekWiseItemEstimateRequest [unitId=" + unitId + ", daysOfWeek=" + daysOfWeek + "]";
	}

	
}
