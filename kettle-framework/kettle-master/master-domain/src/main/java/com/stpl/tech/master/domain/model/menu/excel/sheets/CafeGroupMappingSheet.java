package com.stpl.tech.master.domain.model.menu.excel.sheets;

import com.itextpdf.text.io.StreamUtil;
import com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil;

import java.util.Arrays;
import java.util.List;

public enum CafeGroupMappingSheet {

    GROUP_NAME("Group name",Boolean.TRUE,Boolean.FALSE,String.class,null),
    UNIT_IDS("Cafe IDs (Comma-Separated)",Boolean.TRUE,Boolean.FALSE,String.class,null),
    PRIORITY_LEVEL("Priority Level",Boolean.TRUE,Boolean.FALSE,Integer.class,null),
    STATUS("STATUS",Boolean.TRUE,Boolean.FALSE,String.class, MenuExcelUtil.getAcceptableStatusValues()),
    UPDATE("UPDATE",Boolean.TRUE,Boolean.FALSE,String.class,MenuExcelUtil.getAcceptableUpdateValues()),

    IN_VALID_COLUMN("",null,null,null,null);
    CafeGroupMappingSheet(String columnName , Boolean isMandatory ,Boolean nullable
    , Class<?> dataType , List<?> acceptedValues){
           this.columnName = columnName;
           this.isMandatory = isMandatory;
           this.nullable = nullable;
           this.dataType = dataType;
        this.acceptableValues = acceptedValues;
    }

    private String columnName;

    private Boolean isMandatory;

    private Boolean nullable;

    private Class<?> dataType;

    private List<?> acceptableValues;

    public  String getColumnName(){return this.columnName;}

    public Boolean isMandatory(){return this.isMandatory;}

    public Boolean nullable(){return  this.nullable;}

    public Class<?> getDataType(){return this.dataType;}

    public List<?> getAcceptableValues(){return acceptableValues;}

    public static CafeGroupMappingSheet getColumnEnum(String columnName){
        return Arrays.stream(CafeGroupMappingSheet.values()).filter(column -> column.columnName.equalsIgnoreCase(columnName))
                .findFirst().orElse(CafeGroupMappingSheet.IN_VALID_COLUMN);
    }
}
