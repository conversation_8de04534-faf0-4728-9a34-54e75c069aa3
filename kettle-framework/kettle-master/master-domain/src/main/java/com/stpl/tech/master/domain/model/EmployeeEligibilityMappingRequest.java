package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Request DTO for Employee Eligibility Mapping
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeEligibilityMappingRequest {

    private Long id;

    @NotNull(message = "Eligibility type is required")
    private EligibilityType eligibilityType;

    @NotBlank(message = "Employee Code is required")
    private String employeeCode;

    @NotNull(message = "Mapping type is required")
    private EmployeeEligibilityMappingType mappingType;

    @NotNull(message = "Status is required")
    private SystemStatus status;

    @NotBlank(message = "Value is required")
    private String value;
}
