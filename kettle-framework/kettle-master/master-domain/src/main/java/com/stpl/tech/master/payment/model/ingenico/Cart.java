package com.stpl.tech.master.payment.model.ingenico;

import java.util.ArrayList;

public class Cart {
    ArrayList<CartItem> item = new ArrayList <> ();
    private String reference;
    private String identifier;
    private String description;


    // Getter Methods

    public String getReference() {
        return reference;
    }

    public ArrayList<CartItem> getItem() {
        return item;
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getDescription() {
        return description;
    }

    // Setter Methods

    public void setReference(String reference) {
        this.reference = reference;
    }

    public void setItem(CartItem cartItem) {
        this.item.add(cartItem);
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
