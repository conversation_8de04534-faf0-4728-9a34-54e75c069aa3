/**
 * 
 */
package com.stpl.tech.master.tax.model;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public interface TaxationDetailDao {

	public String getTaxCode();

	public String getTaxType();

	public BigDecimal getTotalTax();

	public BigDecimal getTaxPercentage();

	public BigDecimal getTotalAmount();

	public BigDecimal getTaxableAmount();

	public void setTaxableAmount(BigDecimal taxableAmount);

	public void setTotalAmount(BigDecimal totalAmount);

	public void setTaxPercentage(BigDecimal taxPercentage);

	public void setTotalTax(BigDecimal totalTax);

	public void setTaxType(String taxType);

	public void setTaxCode(String taxCode);
}
