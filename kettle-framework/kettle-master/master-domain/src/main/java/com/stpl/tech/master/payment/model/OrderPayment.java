package com.stpl.tech.master.payment.model;


import java.math.BigDecimal;
import java.util.Date;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 27-03-2017.
 */
public class OrderPayment {

    private Integer orderPaymentDetailId;
    private Integer orderSettlementId;
    private Integer orderId;
    private String externalOrderId;
    private int paymentModeId;
    private String paymentPartner;
    private String paymentSource;
    private PaymentStatus paymentStatus;
    private String refundRequested;
    private PaymentStatus refundStatus;
    private String refundId;
    private String refundReason;
    private String partnerTransactionId;
    private String failureReason;
    private String cartId;
    private String contactNumber;
    private String customerName;
    private Integer customerId;
    private BigDecimal transactionAmount;
    private Date paymentProcessTime;
    private String partnerOrderId;
    private String paymentModeName;
    private String merchantId;
    private Integer brandId;

    public Integer getOrderPaymentDetailId() {
        return orderPaymentDetailId;
    }

    public void setOrderPaymentDetailId(Integer orderPaymentDetailId) {
        this.orderPaymentDetailId = orderPaymentDetailId;
    }

    public Integer getOrderSettlementId() {
        return orderSettlementId;
    }

    public void setOrderSettlementId(Integer orderSettlementId) {
        this.orderSettlementId = orderSettlementId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getExternalOrderId() {
        return externalOrderId;
    }

    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    public int getPaymentModeId() {
        return paymentModeId;
    }

    public void setPaymentModeId(int paymentModeId) {
        this.paymentModeId = paymentModeId;
    }

    public String getPaymentSource() {
        return paymentSource;
    }

    public void setPaymentSource(String paymentSource) {
        this.paymentSource = paymentSource;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getRefundRequested() {
        return refundRequested;
    }

    public void setRefundRequested(String refundRequested) {
        this.refundRequested = refundRequested;
    }

    public PaymentStatus getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(PaymentStatus refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getRefundReason() {
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }

    public String getPartnerTransactionId() {
        return partnerTransactionId;
    }

    public void setPartnerTransactionId(String partnerTransactionId) {
        this.partnerTransactionId = partnerTransactionId;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    public String getCartId() {
        return cartId;
    }

    public void setCartId(String cartId) {
        this.cartId = cartId;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getPaymentPartner() {
        return paymentPartner;
    }

    public void setPaymentPartner(String paymentPartner) {
        this.paymentPartner = paymentPartner;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public Date getPaymentProcessTime() {
        return paymentProcessTime;
    }

    public void setPaymentProcessTime(Date paymentProcessTime) {
        this.paymentProcessTime = paymentProcessTime;
    }

    public String getPartnerOrderId() {
        return partnerOrderId;
    }

    public void setPartnerOrderId(String partnerOrderId) {
        this.partnerOrderId = partnerOrderId;
    }

    public String getPaymentModeName() {
        return paymentModeName;
    }

    public void setPaymentModeName(String paymentModeName) {
        this.paymentModeName = paymentModeName;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }
}
