package com.stpl.tech.master;

import java.io.Serializable;

public class CampaignImageDetail implements Serializable {

    private static final long serialVersionUID = 6118485090209471491L;
    public String name;
    public String url;

    public CampaignImageDetail(String name, String url) {
        this.name = name;
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return "CampaignImageDetail{" +
                "name='" + name + '\'' +
                ", url='" + url + '\'' +
                '}';
    }
}
